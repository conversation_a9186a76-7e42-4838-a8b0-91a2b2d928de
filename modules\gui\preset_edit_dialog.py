#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI配置预设编辑对话框
提供更好的预设编辑体验
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class PresetEditDialog(QDialog):
    """预设编辑对话框"""
    
    def __init__(self, preset_data=None, parent=None):
        super().__init__(parent)
        self.preset_data = preset_data or {}
        self.setup_ui()
        self.setup_style()
        self.load_data()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("编辑AI配置预设")
        self.setModal(True)
        self.setFixedSize(500, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🤖 AI配置预设编辑")
        title_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 表单区域
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(12)
        
        # 预设名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入预设名称")
        form_layout.addRow("预设名称:", self.name_edit)
        
        # API密钥
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText("请输入API密钥")
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        
        # 显示/隐藏密钥按钮
        api_key_layout = QHBoxLayout()
        api_key_layout.addWidget(self.api_key_edit)
        
        self.show_key_btn = QPushButton("👁️")
        self.show_key_btn.setFixedSize(30, 30)
        self.show_key_btn.setCheckable(True)
        self.show_key_btn.clicked.connect(self.toggle_api_key_visibility)
        api_key_layout.addWidget(self.show_key_btn)
        
        api_key_widget = QWidget()
        api_key_widget.setLayout(api_key_layout)
        form_layout.addRow("API密钥:", api_key_widget)
        
        # API地址
        self.api_url_edit = QLineEdit()
        self.api_url_edit.setPlaceholderText("请输入API地址")
        form_layout.addRow("API地址:", self.api_url_edit)
        
        # 模型名称
        self.model_name_edit = QLineEdit()
        self.model_name_edit.setPlaceholderText("请输入模型名称")
        form_layout.addRow("模型名称:", self.model_name_edit)
        
        # 高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout(advanced_group)
        
        # Temperature
        self.temperature_spin = QSpinBox()
        self.temperature_spin.setRange(0, 100)
        self.temperature_spin.setValue(70)
        self.temperature_spin.setSuffix("%")
        advanced_layout.addRow("Temperature:", self.temperature_spin)
        
        # Max Tokens
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 8000)
        self.max_tokens_spin.setValue(4000)
        advanced_layout.addRow("Max Tokens:", self.max_tokens_spin)
        
        # 超时时间
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 300)
        self.timeout_spin.setValue(60)
        self.timeout_spin.setSuffix("秒")
        advanced_layout.addRow("超时时间:", self.timeout_spin)
        
        form_layout.addRow(advanced_group)
        main_layout.addWidget(form_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setFixedSize(80, 35)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setFixedSize(80, 35)
        save_btn.clicked.connect(self.accept)
        button_layout.addWidget(save_btn)
        
        main_layout.addLayout(button_layout)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
            }
            QLineEdit {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 8px;
                color: #ffffff;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
            QSpinBox {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 6px;
                color: #ffffff;
                font-size: 11px;
            }
            QSpinBox:focus {
                border-color: #0078d4;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLabel {
                color: #ffffff;
                font-size: 11px;
            }
            QPushButton {
                background-color: #0078d4;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
    
    def load_data(self):
        """加载数据到界面"""
        if not self.preset_data:
            return
        
        # 加载基本信息
        self.name_edit.setText(self.preset_data.get("name", ""))
        
        # 加载配置
        config = self.preset_data.get("config", {})
        self.api_key_edit.setText(config.get("api_key", ""))
        self.api_url_edit.setText(config.get("api_url", ""))
        self.model_name_edit.setText(config.get("model_name", ""))
        self.temperature_spin.setValue(config.get("temperature", 70))
        self.max_tokens_spin.setValue(config.get("max_tokens", 4000))
        self.timeout_spin.setValue(config.get("timeout", 60))
    
    def get_data(self):
        """获取界面数据"""
        return {
            "name": self.name_edit.text().strip(),
            "config": {
                "api_key": self.api_key_edit.text().strip(),
                "api_url": self.api_url_edit.text().strip(),
                "model_name": self.model_name_edit.text().strip(),
                "temperature": self.temperature_spin.value(),
                "max_tokens": self.max_tokens_spin.value(),
                "timeout": self.timeout_spin.value()
            }
        }
    
    def toggle_api_key_visibility(self):
        """切换API密钥可见性"""
        if self.show_key_btn.isChecked():
            self.api_key_edit.setEchoMode(QLineEdit.Normal)
            self.show_key_btn.setText("🙈")
        else:
            self.api_key_edit.setEchoMode(QLineEdit.Password)
            self.show_key_btn.setText("👁️")
    
    def validate_data(self):
        """验证数据"""
        data = self.get_data()
        
        if not data["name"]:
            QMessageBox.warning(self, "验证失败", "请输入预设名称")
            return False
        
        config = data["config"]
        if not config["api_url"]:
            QMessageBox.warning(self, "验证失败", "请输入API地址")
            return False
        
        if not config["model_name"]:
            QMessageBox.warning(self, "验证失败", "请输入模型名称")
            return False
        
        return True
    
    def accept(self):
        """确认保存"""
        if self.validate_data():
            super().accept()

def show_preset_edit_dialog(preset_data=None, parent=None):
    """显示预设编辑对话框"""
    dialog = PresetEditDialog(preset_data, parent)
    if dialog.exec_() == QDialog.Accepted:
        return dialog.get_data()
    return None
