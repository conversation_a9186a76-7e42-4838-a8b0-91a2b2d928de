#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 目录模式打包脚本
生成文件夹形式的分发包，启动速度更快
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_dir_spec():
    """创建目录模式的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件列表
datas = [
    ('config.json', '.'),
    ('user_config.json', '.'),
    ('suppliers.json', '.'),
    ('smart_memory.json', '.'),
    ('file_manager.json', '.'),
    ('latest_cookies.json', '.'),
    ('智能记忆功能说明.md', '.'),
    ('系统使用指南.md', '.'),
    ('项目完整文档.md', '.'),
    ('ERP_API_文档.md', '.'),
    ('modules', 'modules'),
]

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'requests',
    'pandas',
    'numpy',
    'PIL',
    'pdf2image',
    'loguru',
    'yaml',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='智能票据处理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='智能票据处理系统',
)
'''
    
    with open('智能票据处理系统_dir.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 已创建目录模式spec文件")

def build_dir_exe():
    """执行目录模式打包"""
    print("🚀 开始目录模式打包...")
    
    cmd = [
        'pyinstaller',
        '--clean',
        '智能票据处理系统_dir.spec'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 目录模式打包成功！")
        
        # 创建启动脚本
        create_launcher_script()
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print(f"错误信息: {e.stderr}")
        return False

def create_launcher_script():
    """创建启动脚本"""
    launcher_content = '''@echo off
chcp 65001 >nul
echo ========================================
echo    智能票据处理系统
echo ========================================
echo.
echo 正在启动程序...
echo.

cd /d "%~dp0"
start "" "智能票据处理系统\\智能票据处理系统.exe"

echo ✅ 程序已启动
'''
    
    with open('dist/启动程序.bat', 'w', encoding='gbk') as f:
        f.write(launcher_content)
    print("✅ 已创建启动脚本: dist/启动程序.bat")

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 智能票据处理系统 - 目录模式打包")
    print("=" * 50)
    
    # 清理旧文件
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    # 创建spec文件
    create_dir_spec()
    
    # 执行打包
    if build_dir_exe():
        print("\n🎉 目录模式打包完成！")
        print("📁 分发目录: dist/")
        print("🚀 启动方式: 运行 dist/启动程序.bat")
        print("💡 优势: 启动速度快，文件结构清晰")

if __name__ == "__main__":
    main()
