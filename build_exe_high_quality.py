#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 高质量图标EXE打包脚本
解决图标模糊和空白图标问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理旧文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"   清理文件: {spec_file}")

def find_best_icon():
    """查找最佳图标文件"""
    print("🔍 检查图标文件...")
    
    # 优先使用高分辨率图标
    icon_priorities = [
        'icon/256x256.ico',  # 最高分辨率
        'icon/combined.ico', # 组合图标
        'icon/48x48.ico',    # 中等分辨率
        'icon/32x32.ico',    # 标准分辨率
        'icon/16x16.ico'     # 最小分辨率
    ]
    
    for icon_path in icon_priorities:
        if os.path.exists(icon_path):
            print(f"✅ 选择图标: {icon_path}")
            return icon_path
    
    print("❌ 未找到图标文件")
    return None

def create_high_quality_spec():
    """创建高质量图标的spec文件"""
    print("📝 创建高质量spec文件...")
    
    icon_path = find_best_icon()
    icon_line = f"    icon='{icon_path}'," if icon_path else "    icon=None,"
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# 智能票据处理系统 - 高质量图标配置

block_cipher = None

# 数据文件列表
datas = [
    ('config.json', '.'),
    ('user_config.json', '.'),
    ('suppliers.json', '.'),
    ('smart_memory.json', '.'),
    ('file_manager.json', '.'),
    ('latest_cookies.json', '.'),
    ('ai_config_presets.json', '.'),
    ('智能记忆功能说明.md', '.'),
    ('系统使用指南.md', '.'),
    ('项目完整文档.md', '.'),
    ('ERP_API_文档.md', '.'),
    ('AI配置预设功能说明.md', '.'),
    ('README.md', '.'),
    ('modules', 'modules'),
    ('icon', 'icon'),  # 包含所有图标文件
]

# 隐藏导入 - 确保所有依赖都被包含
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'PyQt5.QtPrintSupport',
    'requests',
    'pandas',
    'numpy',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'pdf2image',
    'loguru',
    'yaml',
    'typing_extensions',
    'json',
    'base64',
    'datetime',
    'threading',
    'asyncio',
    'pathlib',
    're',
    'time',
    'logging',
    'warnings',
    'concurrent.futures',
    # 模块导入
    'modules.ai_config_preset_manager',
    'modules.ai_processor',
    'modules.color_confirm_panel',
    'modules.config_manager',
    'modules.erp_integration',
    'modules.file_manager',
    'modules.image_splitter',
    'modules.memory_settings_tab',
    'modules.product_link_identifier',
    'modules.pyqt5_main_gui',
    'modules.return_rate_calculator',
    'modules.sku_matcher',
    'modules.smart_memory_manager',
    'modules.supplier_manager',
    'modules.table_manager',
    'modules.ui_constants',
    'modules.ui_style_manager',
    'modules.gui',
    'modules.threads',
    'modules.utils',
    'modules.cookies',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智能票据处理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
{icon_line}
    version='version_info.txt',  # 版本信息文件
)
'''
    
    with open('智能票据处理系统_高质量.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ spec文件创建完成")

def create_version_info():
    """创建版本信息文件"""
    print("📋 创建版本信息文件...")
    
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'智能票据处理系统'),
        StringStruct(u'FileDescription', u'智能票据处理系统 - AI驱动的票据自动化处理工具'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'智能票据处理系统'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'智能票据处理系统.exe'),
        StringStruct(u'ProductName', u'智能票据处理系统'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ 版本信息文件创建完成")

def build_exe():
    """执行打包"""
    print("🚀 开始高质量打包...")
    
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        '智能票据处理系统_高质量.spec'
    ]
    
    try:
        print("执行PyInstaller命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ 打包成功！")
        
        # 检查生成的文件
        exe_path = os.path.join('dist', '智能票据处理系统.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 智能票据处理系统 - 高质量图标EXE打包工具")
    print("=" * 60)
    
    # 清理旧文件
    clean_build_dirs()
    
    # 创建版本信息文件
    create_version_info()
    
    # 创建高质量spec文件
    create_high_quality_spec()
    
    # 执行打包
    if build_exe():
        print("\n" + "=" * 60)
        print("🎉 高质量打包完成！")
        print("=" * 60)
        print("📁 可执行文件位置: dist/智能票据处理系统.exe")
        print("🖼️ 图标优化: 使用高分辨率图标，解决模糊问题")
        print("📋 版本信息: 包含完整的文件版本信息")
        print("🚀 程序功能: 包含所有AI配置预设和智能记忆功能")
        print("=" * 60)
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
