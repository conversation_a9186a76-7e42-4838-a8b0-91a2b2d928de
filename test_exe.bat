@echo off
chcp 65001 >nul
echo ========================================
echo    智能票据处理系统 - 打包验证
echo ========================================
echo.

echo 🔍 检查dist目录...
if exist "dist" (
    echo ✅ dist目录存在
    echo.
    echo 📁 dist目录内容:
    dir /b dist
    echo.
) else (
    echo ❌ dist目录不存在
    goto :end
)

echo 🔍 检查EXE文件...
if exist "dist\智能票据处理系统.exe" (
    echo ✅ 找到EXE文件: 智能票据处理系统.exe
    
    echo 📏 文件信息:
    for %%F in ("dist\智能票据处理系统.exe") do (
        echo    大小: %%~zF 字节
        echo    修改时间: %%~tF
    )
    echo.
    
    echo 🎉 打包成功！
    echo 📦 可执行文件位置: dist\智能票据处理系统.exe
    echo.
    
    set /p choice="是否要运行程序进行测试？(y/n): "
    if /i "%choice%"=="y" (
        echo 🚀 启动程序...
        start "" "dist\智能票据处理系统.exe"
    )
) else (
    echo ❌ 未找到EXE文件
)

:end
echo.
echo 按任意键退出...
pause >nul
