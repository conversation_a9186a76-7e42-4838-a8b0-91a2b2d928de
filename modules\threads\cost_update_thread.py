"""
成本价更新线程 - 处理ERP成本价更新
"""

from PyQt5.QtCore import QThread, pyqtSignal


class CostUpdateThread(QThread):
    """成本价更新线程"""
    progress_updated = pyqtSignal(int, int, str)  # 当前项, 总项数, 消息
    item_updated = pyqtSignal(int, bool, str, float, float)  # 行号, 是否成功, 消息, 新成本价, 旧成本价
    update_completed = pyqtSignal(int, int)  # 成功数量, 总数量
    commit_smart_memory = pyqtSignal(bool)  # 是否成功，用于提交智能记忆

    def __init__(self, update_items, erp_integration, table_type):
        super().__init__()
        self.update_items = update_items  # [{"row": 0, "sku_code": "xxx", "sku_id": "xxx", "strategy": "xxx"}, ...]
        self.erp_integration = erp_integration
        self.table_type = table_type

    def run(self):
        """运行成本价更新"""
        try:
            total_items = len(self.update_items)
            success_count = 0

            self.progress_updated.emit(0, total_items, "开始更新成本价...")

            # 🔥 新增：在开始批量更新前进行API预热，确保token有效
            self.progress_updated.emit(0, total_items, "🔥 预热API连接...")
            if not self.erp_integration.warmup_api_connection():
                self.progress_updated.emit(0, total_items, "❌ API预热失败，停止更新")
                self.update_completed.emit(0, total_items)
                return

            self.progress_updated.emit(0, total_items, "✅ API预热成功，开始更新...")

            for i, item in enumerate(self.update_items):
                try:
                    # 检查线程是否被中断
                    if self.isInterruptionRequested():
                        self.progress_updated.emit(i, total_items, "更新被中断")
                        break

                    row = item["row"]
                    sku_code = item["sku_code"]
                    sku_id = item["sku_id"]
                    strategy = item["strategy"]
                    ticket_price = item.get("ticket_price", "0")  # 获取票据单价

                    self.progress_updated.emit(i + 1, total_items, f"更新: {sku_code}")

                    # 根据策略决定成本价
                    # 使用票据中的单价作为新的成本价
                    cost_price = self._get_recommended_cost_price(sku_id, strategy, ticket_price)

                    if cost_price is not None:
                        # 获取旧的成本价（从ERP匹配结果中获取）
                        old_cost_price = None
                        color_spec_skus = item.get("color_spec_skus", [])

                        if color_spec_skus:
                            # 从第一个SKU获取旧成本价
                            old_cost_price = color_spec_skus[0].get("cost_price", None)

                        # 价格比较逻辑：如果ERP成本价与票据价格一致，跳过更新
                        if old_cost_price is not None and old_cost_price != "" and old_cost_price != 0:
                            try:
                                old_price_float = float(old_cost_price)
                                new_price_float = float(cost_price)

                                if abs(old_price_float - new_price_float) < 0.01:  # 价格相同（允许0.01的误差）
                                    self.item_updated.emit(row, True, "✅ 无变化", cost_price, old_cost_price)
                                    success_count += 1
                                    continue  # 跳过实际更新操作
                            except (ValueError, TypeError):
                                pass  # 价格转换失败，继续正常更新流程

                        if color_spec_skus and len(color_spec_skus) >= 1:
                            # 批量更新同一颜色规格的所有尺码（包括单个尺码的情况）
                            color_name = color_spec_skus[0].get("color", "未知颜色")
                            self.progress_updated.emit(i + 1, total_items, f"批量更新颜色规格: {color_name}")

                            # 🚀 使用优化版本的成本价更新（自动选择最佳策略）
                            # 根据SKU数量自动选择串行或并发模式
                            results = self.erp_integration.update_color_spec_cost_price_optimized(
                                color_spec_skus,
                                cost_price,
                                lambda current, total, desc: self.progress_updated.emit(
                                    i + 1, total_items, f"更新 {desc}: {current}/{total}"
                                )
                            )

                            # 统计结果
                            success_count_batch = sum(1 for success in results.values() if success)
                            total_count_batch = len(results)

                            if success_count_batch > 0:
                                # 根据原价格情况确定状态
                                if old_cost_price is None or old_cost_price == "" or old_cost_price == 0:
                                    status = f"✅ 新增 ({success_count_batch}/{total_count_batch})"
                                else:
                                    # 计算价格变化
                                    try:
                                        old_price_float = float(old_cost_price)
                                        new_price_float = float(cost_price)
                                        difference = new_price_float - old_price_float

                                        if difference > 0.01:
                                            status = f"📈 ¥{difference:.1f} ({success_count_batch}/{total_count_batch})"
                                        elif difference < -0.01:
                                            status = f"📉 ¥{abs(difference):.1f} ({success_count_batch}/{total_count_batch})"
                                        else:
                                            status = f"✅ 无变化 ({success_count_batch}/{total_count_batch})"
                                    except (ValueError, TypeError):
                                        status = f"✅ 新增 ({success_count_batch}/{total_count_batch})"

                                self.item_updated.emit(row, True, status, cost_price, old_cost_price)
                                success_count += 1
                            else:
                                self.item_updated.emit(row, False, f"批量更新失败 (0/{total_count_batch})", 0.0, old_cost_price)
                        else:
                            # 单个SKU更新
                            success = self.erp_integration.update_cost_price(sku_id, cost_price)

                            if success:
                                # 根据原价格情况确定状态
                                if old_cost_price is None or old_cost_price == "" or old_cost_price == 0:
                                    status = "✅ 新增"  # 原价格为空，此次新增成本价
                                else:
                                    # 计算价格变化
                                    try:
                                        old_price_float = float(old_cost_price)
                                        new_price_float = float(cost_price)
                                        difference = new_price_float - old_price_float

                                        if difference > 0.01:
                                            status = f"📈 ¥{difference:.1f}"  # 价格上涨，红字
                                        elif difference < -0.01:
                                            status = f"📉 ¥{abs(difference):.1f}"  # 价格下降，绿字
                                        else:
                                            status = "✅ 无变化"  # 价格相同
                                    except (ValueError, TypeError):
                                        status = "✅ 新增"  # 转换失败，默认显示新增

                                self.item_updated.emit(row, True, status, cost_price, old_cost_price)
                                success_count += 1
                            else:
                                self.item_updated.emit(row, False, "ERP更新失败", 0.0, old_cost_price)
                    else:
                        self.item_updated.emit(row, False, "无法获取推荐成本价", 0.0, None)

                    # 添加延时避免频率限制
                    self.msleep(300)  # 300毫秒延时

                except Exception as e:
                    try:
                        self.item_updated.emit(item.get("row", 0), False, f"更新出错: {str(e)}", 0.0, None)
                        self.progress_updated.emit(i + 1, total_items, f"更新第{item.get('row', 0)+1}行出错")
                    except Exception as signal_error:
                        pass

            self.progress_updated.emit(total_items, total_items, "更新完成")
            self.update_completed.emit(success_count, total_items)

            # 🧠 智能记忆：如果有成功的更新，发送提交信号
            self.commit_smart_memory.emit(success_count > 0)

        except Exception as e:
            try:
                self.progress_updated.emit(0, 0, f"更新过程出错: {str(e)}")
                self.update_completed.emit(0, len(self.update_items))
            except:
                pass

    def _get_recommended_cost_price(self, sku_id, strategy, ticket_item_price):
        """获取推荐的成本价（真实逻辑）"""
        try:
            # 根据ERP成本价更新逻辑文档，成本价来源是票据中的单价
            # 这是用户想要设置的新成本价

            if strategy == "auto_update":
                # 自动更新策略：直接使用票据中的单价作为新成本价
                return float(ticket_item_price)
            elif strategy == "smart_recommend":
                # 智能推荐策略：也使用票据中的单价作为新成本价
                return float(ticket_item_price)
            elif strategy == "manual_confirm":
                # 人工确认策略：也使用票据中的单价作为新成本价
                # 注意：到这里说明用户已经确认了要更新，所以可以使用票据单价
                return float(ticket_item_price)
            else:
                return None
        except Exception as e:
            return None 