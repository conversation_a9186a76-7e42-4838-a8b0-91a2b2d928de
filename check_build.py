#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查打包结果
"""

import os
import sys
from pathlib import Path

def main():
    print("🔍 检查打包结果")
    print("=" * 40)
    
    # 检查dist目录
    dist_dir = Path("dist")
    if dist_dir.exists():
        print(f"✅ dist目录存在")
        
        # 列出dist目录中的文件
        files = list(dist_dir.iterdir())
        if files:
            print(f"📁 dist目录包含 {len(files)} 个文件:")
            for file in files:
                if file.is_file():
                    size_mb = file.stat().st_size / (1024 * 1024)
                    print(f"   📦 {file.name} ({size_mb:.1f} MB)")
                else:
                    print(f"   📁 {file.name} (目录)")
        else:
            print("❌ dist目录为空")
    else:
        print("❌ dist目录不存在")
    
    # 检查特定的exe文件
    exe_file = dist_dir / "智能票据处理系统.exe"
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"\n🎉 打包成功！")
        print(f"📦 文件: {exe_file}")
        print(f"📏 大小: {size_mb:.1f} MB")
        print(f"📅 修改时间: {exe_file.stat().st_mtime}")
        
        # 检查文件是否可执行
        if os.access(exe_file, os.X_OK):
            print("✅ 文件具有执行权限")
        else:
            print("⚠️ 文件可能没有执行权限")
            
    else:
        print("\n❌ 未找到智能票据处理系统.exe文件")
    
    # 检查build目录
    build_dir = Path("build")
    if build_dir.exists():
        print(f"\n📁 build目录存在 (临时文件)")
    
    # 检查spec文件
    spec_files = list(Path(".").glob("*.spec"))
    if spec_files:
        print(f"\n📄 找到 {len(spec_files)} 个spec文件:")
        for spec in spec_files:
            print(f"   - {spec.name}")

if __name__ == "__main__":
    main()
