#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型卡片组件
用于显示和管理AI配置预设
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime

class AIModelCard(QWidget):
    """AI模型配置卡片"""
    
    # 信号定义
    load_config_requested = pyqtSignal(str)  # 加载配置请求
    delete_preset_requested = pyqtSignal(str)  # 删除预设请求
    edit_preset_requested = pyqtSignal(str)  # 编辑预设请求
    
    def __init__(self, preset_data: dict, parent=None):
        super().__init__(parent)
        self.preset_data = preset_data
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置UI布局"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(8)
        
        # 卡片头部
        self.create_header(main_layout)
        
        # 卡片内容
        self.create_content(main_layout)
        
        # 卡片底部操作按钮
        self.create_actions(main_layout)
    
    def create_header(self, layout):
        """创建卡片头部"""
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # 模型名称
        self.name_label = QLabel(self.preset_data["name"])
        self.name_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
                margin: 0px;
            }
        """)
        header_layout.addWidget(self.name_label)
        
        # 状态指示器
        self.status_label = QLabel()
        self.update_status_indicator()
        header_layout.addWidget(self.status_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
    
    def create_content(self, layout):
        """创建卡片内容"""
        content_layout = QVBoxLayout()
        content_layout.setSpacing(6)
        
        config = self.preset_data["config"]
        
        # API URL
        api_url = config.get("api_url", "")
        if len(api_url) > 50:
            api_url = api_url[:47] + "..."
        
        self.api_url_label = QLabel(f"🌐 {api_url}")
        self.api_url_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                color: #cccccc;
                margin: 0px;
            }
        """)
        content_layout.addWidget(self.api_url_label)
        
        # API Key
        api_key = config.get("api_key", "")
        api_key_display = self.format_api_key(api_key)
        
        self.api_key_label = QLabel(f"🔑 {api_key_display}")
        self.api_key_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                color: #cccccc;
                margin: 0px;
            }
        """)
        content_layout.addWidget(self.api_key_label)
        
        # 模型信息
        model_name = config.get("model_name", "未设置")
        self.model_label = QLabel(f"🤖 {model_name}")
        self.model_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                color: #cccccc;
                margin: 0px;
            }
        """)
        content_layout.addWidget(self.model_label)
        
        # 创建时间
        created_time = self.preset_data.get("created_time")
        time_str = self.format_time(created_time)
        
        self.time_label = QLabel(f"📅 {time_str}")
        self.time_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 10px;
                color: #999999;
                margin: 0px;
            }
        """)
        content_layout.addWidget(self.time_label)
        
        layout.addLayout(content_layout)
    
    def create_actions(self, layout):
        """创建操作按钮"""
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 8, 0, 0)
        actions_layout.setSpacing(8)
        
        # 加载配置按钮
        self.load_btn = QPushButton("🚀 加载配置")
        self.load_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                border: none;
                border-radius: 4px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 10px;
                font-weight: bold;
                padding: 6px 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        self.load_btn.clicked.connect(self.on_load_config)
        actions_layout.addWidget(self.load_btn)
        
        # 编辑按钮
        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                border: none;
                border-radius: 4px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 10px;
                padding: 6px 12px;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        self.edit_btn.clicked.connect(self.on_edit_preset)
        actions_layout.addWidget(self.edit_btn)
        
        # 删除按钮
        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                border: none;
                border-radius: 4px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 10px;
                padding: 6px 12px;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        self.delete_btn.clicked.connect(self.on_delete_preset)
        actions_layout.addWidget(self.delete_btn)
        
        actions_layout.addStretch()
        layout.addLayout(actions_layout)
    
    def setup_style(self):
        """设置卡片样式"""
        self.setStyleSheet("""
            AIModelCard {
                background-color: #2d2d30;
                border: 1px solid #3a3a3a;
                border-radius: 8px;
                margin: 4px;
            }
            AIModelCard:hover {
                border-color: #0078d4;
                background-color: #323235;
            }
        """)
        
        # 设置固定大小
        self.setFixedSize(280, 160)
        
        # 设置鼠标悬停效果
        self.setAttribute(Qt.WA_Hover, True)
    
    def format_api_key(self, api_key: str) -> str:
        """格式化API密钥显示"""
        if not api_key:
            return "未设置"
        
        if len(api_key) <= 8:
            return "*" * len(api_key)
        
        return f"{api_key[:4]}{'*' * (len(api_key) - 8)}{api_key[-4:]}"
    
    def format_time(self, timestamp) -> str:
        """格式化时间显示"""
        if not timestamp:
            return "未知时间"
        
        try:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime("%m-%d %H:%M")
        except:
            return "时间错误"
    
    def update_status_indicator(self):
        """更新状态指示器"""
        config = self.preset_data["config"]
        api_key = config.get("api_key", "")
        api_url = config.get("api_url", "")
        
        if api_key and api_url:
            # 配置完整
            self.status_label.setText("🟢")
            self.status_label.setToolTip("配置完整")
        elif api_key or api_url:
            # 配置不完整
            self.status_label.setText("🟡")
            self.status_label.setToolTip("配置不完整")
        else:
            # 未配置
            self.status_label.setText("🔴")
            self.status_label.setToolTip("未配置")
    
    def on_load_config(self):
        """加载配置按钮点击事件"""
        self.load_config_requested.emit(self.preset_data["name"])
    
    def on_edit_preset(self):
        """编辑预设按钮点击事件"""
        self.edit_preset_requested.emit(self.preset_data["name"])
    
    def on_delete_preset(self):
        """删除预设按钮点击事件"""
        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除预设 '{self.preset_data['name']}' 吗？\n\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.delete_preset_requested.emit(self.preset_data["name"])
    
    def update_preset_data(self, preset_data: dict):
        """更新预设数据"""
        self.preset_data = preset_data
        self.refresh_display()
    
    def refresh_display(self):
        """刷新显示内容"""
        # 更新名称
        self.name_label.setText(self.preset_data["name"])
        
        # 更新状态指示器
        self.update_status_indicator()
        
        # 更新内容（重新创建内容区域）
        # 这里可以优化为直接更新标签文本
        config = self.preset_data["config"]
        
        # 更新API URL
        api_url = config.get("api_url", "")
        if len(api_url) > 50:
            api_url = api_url[:47] + "..."
        self.api_url_label.setText(f"🌐 {api_url}")
        
        # 更新API Key
        api_key = config.get("api_key", "")
        api_key_display = self.format_api_key(api_key)
        self.api_key_label.setText(f"🔑 {api_key_display}")
        
        # 更新模型名称
        model_name = config.get("model_name", "未设置")
        self.model_label.setText(f"🤖 {model_name}")
        
        # 更新时间
        created_time = self.preset_data.get("created_time")
        time_str = self.format_time(created_time)
        self.time_label.setText(f"📅 {time_str}")
