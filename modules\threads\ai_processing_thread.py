"""
AI处理线程 - 负责后台AI图像解析处理
"""

import os
from PyQt5.QtCore import QThread, pyqtSignal

from modules.ai_processor import AIProcessor


class AIProcessingThread(QThread):
    """AI处理线程"""
    progress_updated = pyqtSignal(int, str)  # 进度, 消息
    result_ready = pyqtSignal(str, dict)     # 文件路径, 结果

    def __init__(self, files, ai_config, prompt):
        super().__init__()
        self.files = files
        self.ai_config = ai_config
        self.prompt = prompt
        self.ai_processor = AIProcessor()

    def run(self):
        """运行AI处理"""
        import time
        import os

        for i, file_path in enumerate(self.files):
            try:
                # 降级为DEBUG级别，这是处理进度信息
                self.progress_updated.emit(i, f"[DEBUG] 正在处理: {os.path.basename(file_path)}")

                # 🔥 新增：验证文件存在性
                if not os.path.exists(file_path):
                    error_result = {
                        "success": False,
                        "error": f"文件不存在: {file_path}",
                        "file_path": file_path
                    }
                    self.result_ready.emit(file_path, error_result)
                    self.progress_updated.emit(i, f"❌ 文件不存在: {os.path.basename(file_path)}")
                    continue

                # 配置AI处理器
                self.ai_processor.configure(self.ai_config)

                # 🔥 新增：添加处理间隔，避免API频率限制
                if i > 0:
                    time.sleep(1.5)  # 1.5秒间隔

                # 处理文件
                result = self.ai_processor.process_image(file_path, self.prompt)

                # 🔥 增强：检查结果有效性
                if result is None:
                    error_result = {
                        "success": False,
                        "error": "AI处理返回空结果",
                        "file_path": file_path
                    }
                    self.result_ready.emit(file_path, error_result)
                    self.progress_updated.emit(i, f"❌ 处理返回空结果: {os.path.basename(file_path)}")
                else:
                    self.result_ready.emit(file_path, result)

                    # 根据结果显示不同的进度信息
                    if result.get("success", False):
                        self.progress_updated.emit(i, f"✅ 处理成功: {os.path.basename(file_path)}")
                    else:
                        error_msg = result.get("error", "未知错误")
                        self.progress_updated.emit(i, f"❌ 处理失败: {os.path.basename(file_path)} - {error_msg}")

            except Exception as e:
                error_result = {
                    "success": False,
                    "error": str(e),
                    "file_path": file_path
                }
                self.result_ready.emit(file_path, error_result)
                self.progress_updated.emit(i, f"❌ 处理异常 {os.path.basename(file_path)}: {str(e)}")

        # 降级为DEBUG级别，这是处理完成信息
        self.progress_updated.emit(len(self.files), "[DEBUG] 批量处理完成")