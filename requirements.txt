# 智能票据处理系统依赖包
# 基于代码分析的实际依赖清单

# ===========================================
# 🎨 GUI界面框架 (核心依赖)
# ===========================================
PyQt5>=5.15.0
PyQt5-tools>=5.15.0

# ===========================================
# 🌐 网络请求和API调用
# ===========================================
requests>=2.28.0

# ===========================================
# 🖼️ 图像处理
# ===========================================
Pillow>=9.0.0
pdf2image>=1.16.0

# ===========================================
# 📊 数据处理和分析
# ===========================================
pandas>=1.5.0
numpy>=1.21.0

# ===========================================
# 📝 日志和调试
# ===========================================
loguru>=0.6.0

# ===========================================
# ⚙️ 配置文件处理
# ===========================================
pyyaml>=6.0

# ===========================================
# 🔧 开发工具和类型提示
# ===========================================
typing-extensions>=4.0.0

# ===========================================
# 🧪 测试和开发依赖 (可选)
# ===========================================
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0
# mypy>=0.950

# ===========================================
# 📦 Python内置模块 (无需安装)
# ===========================================
# json - JSON数据处理
# base64 - 图像编码
# datetime - 时间处理
# os - 操作系统接口
# sys - 系统参数
# threading - 多线程
# asyncio - 异步编程
# pathlib - 路径操作
# re - 正则表达式
# time - 时间函数
# logging - 日志记录
# warnings - 警告控制
# concurrent.futures - 并发执行
