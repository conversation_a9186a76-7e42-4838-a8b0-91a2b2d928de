# 🎯 智能票据处理系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production-brightgreen.svg)]()

一个基于AI和PyQt5开发的企业级智能票据处理系统，能够自动识别和解析票据图像，提取商品信息，并与ERP系统集成进行成本价管理和利润分析。

## ✨ 系统特色

### 🎯 **核心价值**
- **🚀 效率提升**: AI自动解析票据，减少90%手动录入工作
- **💰 成本控制**: 实时利润计算，精确成本价管理
- **🧠 智能记忆**: 学习用户偏好，自动化重复操作
- **🔒 数据安全**: 完整的状态隔离和数据保护机制

### 🌟 **技术亮点**
- **模块化架构**: 从6557行单文件重构为7个专门模块
- **多线程处理**: AI解析、ERP查询、成本更新并行执行
- **智能匹配**: 基于编辑距离的SKU智能匹配算法
- **状态管理**: 解决多文件数据污染的核心技术方案

---

## 🚀 核心功能

### 🤖 **AI智能解析**
- **多模型支持**: doubao-1.5-vision-lite、GPT-4V等主流视觉AI模型
- **智能识别**: 自动识别票据中的商品信息（款号、颜色规格、数量、单价等）
- **格式兼容**: 支持JPG、PNG、BMP、PDF等多种票据格式
- **图像分割**: 智能图像分割功能，处理长票据和复杂布局
- **数据标准化**: 结构化数据提取和格式标准化处理

### 🔍 **ERP系统集成**
- **实时查询**: 实时查询ERP商品信息和价格数据
- **智能匹配**: 基于编辑距离的智能SKU匹配算法
- **批量操作**: 支持批量成本价更新和库存查询
- **状态管理**: 完整的查询状态跟踪和结果缓存
- **认证管理**: 自动Cookie管理和认证状态检查

### 💰 **成本价管理**
- **利润计算**: 自动计算商品利润和毛利率
- **颜色确认**: 可视化颜色规格确认面板
- **价格对比**: 实时显示ERP售价与票据成本价对比
- **状态显示**: 价格上涨🔴、价格下降🟢的直观显示
- **批量更新**: 支持选择性批量更新ERP系统成本价

### 🧠 **智能记忆功能** ⭐
- **自动记忆**: 记录用户在颜色确认面板的选择偏好
- **智能匹配**: 基于款号+颜色规格+单价的精确匹配
- **自动选择**: 下次遇到相同条件时自动选择对应商品
- **安全机制**: 只在成本价上传成功后保存记忆，避免误操作
- **管理界面**: 提供记忆统计、列表查看和数据管理功能

### 🎨 **现代化界面**
- **深色主题**: 基于PyQt5的美观界面设计，护眼黑色主题
- **模块化布局**: 标签页式布局，功能区域清晰分离
- **图像管理**: 完整的图像预览、缩略图和全屏显示功能
- **进度反馈**: 统一的进度条显示，实时操作状态反馈
- **响应式设计**: 自适应布局和流畅的交互体验

### 🔒 **数据状态管理**
- **状态隔离**: 完整的表格数据状态隔离机制
- **持久化**: 多文件处理时的状态持久化和恢复
- **精确匹配**: 基于商品唯一标识的精确匹配算法
- **污染防护**: 避免数据污染的核心技术方案

---

## 🚀 快速开始

### 📋 **系统要求**
- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: 3.8 或更高版本
- **内存**: 至少 4GB RAM (推荐 8GB+)
- **网络**: 稳定的互联网连接（AI服务和ERP查询）
- **显示器**: 1920x1080 或更高分辨率

### 📦 **安装步骤**

#### **方式一：快速安装（推荐）**
```bash
# 1. 克隆项目
git clone [项目地址]
cd 智能票据处理系统

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动程序
python main.py
# 或双击 启动程序.bat
```

#### **方式二：虚拟环境安装**
```bash
# 1. 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动程序
python main.py
```

### ⚙️ **首次配置**
1. 启动程序后，进入"配置"标签页
2. 配置AI API密钥和模型参数
3. 设置ERP系统访问参数
4. 配置供应商信息（可选）
5. 测试连接确保配置正确

---

## 📁 项目架构

### 📂 **完整目录结构**
```
智能票据处理系统/
├── 📄 main.py                          # 🚀 程序启动入口
├── 🚀 启动程序.bat                     # Windows快速启动脚本
├── 📋 requirements.txt                 # Python依赖包列表
├── 📄 README.md                        # 项目说明文档（本文件）
├── 📄 项目完整文档.md                  # 完整技术文档和架构说明
├── 📄 ERP_API_文档.md                  # ERP API接口详细文档
├── 📄 智能记忆功能说明.md              # 智能记忆功能使用说明
├── 📄 系统使用指南.md                  # 详细使用指南
│
├── 📂 modules/                         # 🎯 核心功能模块目录
│   ├── 📄 __init__.py                  # 模块包初始化
│   ├── 🎨 pyqt5_main_gui.py           # 主界面入口（重构后轻量版）
│   ├── 🤖 ai_processor.py             # AI解析处理核心
│   ├── 🔍 erp_integration.py          # ERP系统集成接口
│   ├── 🎯 sku_matcher.py              # 智能SKU匹配算法
│   ├── ⚙️ config_manager.py           # 配置管理器
│   ├── 📁 file_manager.py             # 文件管理器
│   ├── 🖼️ image_splitter.py           # 图像分割处理
│   ├── 🏢 supplier_manager.py         # 供应商管理
│   ├── 🎨 color_confirm_panel.py      # 颜色确认面板
│   ├── 📊 table_manager.py            # 表格数据管理
│   ├── 🧠 smart_memory_manager.py     # 智能记忆管理器
│   ├── 🎨 ui_style_manager.py         # UI样式管理器
│   ├── 🎨 ui_constants.py             # UI常量定义
│   ├── 📊 return_rate_calculator.py   # 退货率计算器
│   ├── 🔗 product_link_identifier.py  # 商品链接识别器
│   ├── ⚙️ memory_settings_tab.py      # 智能记忆设置页面
│   │
│   ├── 📂 gui/                        # 🎨 GUI界面组件（重构后）
│   │   ├── 📄 __init__.py             # GUI模块包初始化
│   │   ├── 🎮 main_gui_controller.py  # 主界面控制器
│   │   ├── 🖼️ image_manager.py        # 图像管理器
│   │   ├── 📊 table_ui_manager.py     # 表格界面管理器
│   │   ├── 🤖 ai_processor_ui.py      # AI处理界面
│   │   ├── ⚙️ config_ui_manager.py    # 配置界面管理器
│   │   ├── 🔍 erp_ui_manager.py       # ERP界面管理器
│   │   ├── 🎯 event_handlers.py       # 事件处理器
│   │   ├── 🖼️ image_card_widget.py    # 图像卡片组件
│   │   ├── 🍪 cookies_manager_widget.py # Cookie管理组件
│   │   └── 🖼️ image_split_overlay.py  # 图像分割覆盖层
│   │
│   ├── 📂 threads/                    # 🔄 多线程处理模块
│   │   ├── 📄 __init__.py             # 线程模块包初始化
│   │   ├── 🤖 ai_processing_thread.py # AI处理线程
│   │   ├── 🔍 erp_query_thread.py     # ERP查询线程
│   │   ├── 💰 cost_update_thread.py   # 成本更新线程
│   │   └── 📝 thread_safe_logger.py   # 线程安全日志器
│   │
│   ├── 📂 cookies/                    # 🍪 Cookie管理模块
│   │   ├── 📄 __init__.py             # Cookie模块包初始化
│   │   ├── 🍪 manager.py              # Cookie管理器
│   │   ├── 🔍 parser.py               # Cookie解析器
│   │   ├── ✅ validator.py            # Cookie验证器
│   │   └── ❌ exceptions.py           # Cookie异常处理
│   │
│   └── 📂 utils/                      # 🛠️ 工具模块
│       ├── 📄 __init__.py             # 工具模块包初始化
│       └── 📝 logger.py               # 统一日志管理器
│
├── 📂 __pycache__/                    # Python缓存文件（自动生成）
├── 📂 temp_segments/                  # 临时图像片段存储（自动清理）
│
├── 📄 config.json                     # 系统配置文件
├── 📄 user_config.json                # 用户个人配置文件
├── 📄 suppliers.json                  # 供应商数据库
├── 📄 file_manager.json               # 文件管理配置
├── 📄 smart_memory.json               # 智能记忆数据存储
└── 📄 latest_cookies.json             # ERP系统访问Cookie
```

### 🏗️ **架构设计原则**

#### **模块化重构成果**
- **重构前**: 单文件6557行代码，难以维护
- **重构后**: 7个GUI模块 + 多个功能模块，职责清晰
- **优势**: 代码可读性提升、便于功能定位、降低维护成本

#### **核心模块职责**
- **🎮 main_gui_controller.py**: 主界面控制器，负责窗口和标签页管理
- **🖼️ image_manager.py**: 图像管理器，处理图像预览、缩略图、全屏显示
- **📊 table_ui_manager.py**: 表格界面管理器，负责结果表格和数据显示
- **🤖 ai_processor_ui.py**: AI处理界面，处理AI解析相关界面逻辑
- **⚙️ config_ui_manager.py**: 配置界面管理器，管理所有配置页面
- **🔍 erp_ui_manager.py**: ERP界面管理器，处理ERP查询和更新界面
- **🎯 event_handlers.py**: 事件处理器，统一处理用户交互事件

---

## 🛠️ 技术栈

### **核心技术**
- **界面框架**: PyQt5 5.15+ (主界面、组件、事件处理)
- **图像处理**: Pillow 9.0+ (图像编码、分割、预览)
- **HTTP请求**: requests 2.28+ (AI API调用、ERP接口通信)
- **数据处理**: pandas 1.5+, numpy 1.21+ (数据分析和处理)
- **多线程**: QThread (AI处理、ERP查询、成本更新线程)
- **日志系统**: loguru 0.6+ (统一日志管理)

### **开发工具**
- **类型提示**: typing-extensions 4.0+
- **配置管理**: PyYAML 6.0+
- **PDF处理**: pdf2image 1.16+

---

## 📖 使用指南

### 🎯 **基本操作流程**
1. **上传票据** → 选择票据图像文件
2. **AI解析** → 自动识别商品信息
3. **ERP查询** → 查询商品价格和库存
4. **颜色确认** → 确认商品颜色规格
5. **成本更新** → 批量更新ERP成本价

### 🔧 **高级功能**
- **图像分割**: 处理长票据，自定义分割区域
- **智能记忆**: 自动记录和应用用户选择偏好
- **批量处理**: 同时处理多个票据文件
- **状态管理**: 保存和恢复处理状态
- **数据导出**: 导出处理结果到Excel

### 📚 **详细文档**
- **📄 系统使用指南.md**: 详细的操作说明和功能介绍
- **📄 智能记忆功能说明.md**: 智能记忆功能的使用方法
- **📄 ERP_API_文档.md**: ERP API接口技术文档
- **📄 项目完整文档.md**: 完整的技术架构和开发文档

---

## 🔧 开发指南

### **环境搭建**
```bash
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 开发模式启动
python main.py
```

### **代码规范**
- 使用Python 3.8+语法
- 遵循PEP 8代码规范
- 添加类型提示
- 完善文档字符串

### **测试**
- 单元测试覆盖核心功能
- 集成测试验证完整流程
- 性能测试确保响应速度

---

## 📝 更新日志

### **v2.1.0 (2024-12-23)**
- ✅ 完成智能记忆系统优化，数据结构简化
- ✅ 修复ERP UI管理器智能记忆自动确认问题
- ✅ 更新项目文档和依赖管理
- ✅ 代码整理和架构优化

### **v2.0.0 (2024-07-09)**
- ✅ 重构GUI架构，从6557行单文件拆分为7个模块
- ✅ 完善状态隔离机制，解决数据污染问题
- ✅ 优化退货率计算，支持商品链接级别计算
- ✅ 修复商品链接识别逻辑，改为精确匹配

---

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目采用MIT许可证。

---

**开发团队**: 一介哥专用AI助手  
**最后更新**: 2024年12月23日
