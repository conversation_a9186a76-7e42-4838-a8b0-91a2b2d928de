#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆设置标签页 - 简单的记忆管理界面
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QGroupBox, QTextEdit, QHeaderView, QMessageBox,
                             QFileDialog, QDialog, QFormLayout, QLineEdit,
                             QDoubleSpinBox, QCheckBox, QDialogButtonBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from modules.smart_memory_manager import SmartMemoryManager
from datetime import datetime
import json


class MemorySettingsTab(QWidget):
    """智能记忆设置标签页"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.smart_memory = SmartMemoryManager()
        self.setup_ui()
        self.setup_dark_theme()
        self.refresh_memory_data()
        
        # 定时刷新（每30秒）
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_memory_data)
        self.refresh_timer.start(30000)  # 30秒
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("🧠 智能记忆管理")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 记忆列表组（铺满高度）
        self.create_memory_list_group(layout)

        # 操作按钮组
        self.create_action_buttons(layout)
    

    
    def create_memory_list_group(self, parent_layout):
        """创建记忆列表组（铺满高度）"""
        list_group = QGroupBox("📝 记忆列表")
        list_layout = QVBoxLayout(list_group)

        # 搜索框区域
        search_layout = QHBoxLayout()
        search_label = QLabel("🔍 搜索:")
        search_label.setMinimumWidth(50)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入款号、单价或颜色规格进行模糊搜索...")
        self.search_input.textChanged.connect(self.on_search_text_changed)

        # 清空搜索按钮
        clear_search_btn = QPushButton("✖")
        clear_search_btn.setMaximumWidth(30)
        clear_search_btn.setToolTip("清空搜索")
        clear_search_btn.clicked.connect(self.clear_search)
        clear_search_btn.setStyleSheet("QPushButton { background-color: #757575; }")

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(clear_search_btn)

        list_layout.addLayout(search_layout)

        # 记忆表格
        self.memory_table = QTableWidget()
        self.memory_table.setColumnCount(5)
        self.memory_table.setHorizontalHeaderLabels([
            "款号", "单价", "选择颜色数", "总颜色数", "已选择颜色规格详情"
        ])

        # 设置表格属性
        header = self.memory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 款号
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 单价
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 选择颜色数
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 总颜色数
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # 已选择颜色规格详情

        self.memory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.memory_table.setAlternatingRowColors(True)
        self.memory_table.setSortingEnabled(True)

        list_layout.addWidget(self.memory_table)

        # 初始化搜索相关变量
        self.all_memory_data = {}  # 存储所有记忆数据
        self.filtered_memory_data = {}  # 存储过滤后的数据

        # 设置组框拉伸因子，让表格铺满高度
        parent_layout.addWidget(list_group, 1)  # 拉伸因子为1
    
    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()

        # 添加按钮（绿色）
        add_btn = QPushButton("➕ 添加记忆")
        add_btn.clicked.connect(self.add_memory_rule)
        add_btn.setStyleSheet("QPushButton { background-color: #4caf50; }")
        button_layout.addWidget(add_btn)

        # 删除按钮（红色）
        delete_btn = QPushButton("🗑️ 删除选中")
        delete_btn.clicked.connect(self.delete_selected_memory)
        delete_btn.setStyleSheet("QPushButton { background-color: #f44336; }")
        button_layout.addWidget(delete_btn)

        # 导入按钮（蓝色）
        import_btn = QPushButton("📥 导入记忆")
        import_btn.clicked.connect(self.import_memory_data)
        import_btn.setStyleSheet("QPushButton { background-color: #2196f3; }")
        button_layout.addWidget(import_btn)

        # 导出按钮（蓝色）
        export_btn = QPushButton("📤 导出记忆")
        export_btn.clicked.connect(self.export_memory_data)
        export_btn.setStyleSheet("QPushButton { background-color: #2196f3; }")
        button_layout.addWidget(export_btn)

        # 刷新按钮（橙色）
        refresh_btn = QPushButton("🔄 刷新表格")
        refresh_btn.clicked.connect(self.refresh_table_manually)
        refresh_btn.setStyleSheet("QPushButton { background-color: #ff9800; }")
        button_layout.addWidget(refresh_btn)

        button_layout.addStretch()
        parent_layout.addLayout(button_layout)
    
    def setup_dark_theme(self):
        """设置深色主题"""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QTableWidget {
                background-color: #3c3c3c;
                alternate-background-color: #454545;
                selection-background-color: #6a4c93;
                gridline-color: #555555;
                border: 1px solid #555555;
                border-radius: 5px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            
            QHeaderView::section {
                background-color: #4a4a4a;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            
            QPushButton {
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 120px;
            }

            QPushButton:hover {
                opacity: 0.8;
            }

            QPushButton:pressed {
                opacity: 0.6;
            }
            
            QLabel {
                color: #ffffff;
            }
        """)
    
    def refresh_memory_data(self):
        """刷新记忆数据（优化版，支持搜索）"""
        try:
            print("🔄 开始刷新记忆数据...")

            # 重新加载记忆数据
            self.smart_memory.load_memory_data()
            print(f"📊 加载记忆数据完成，共 {len(self.smart_memory.memory_data)} 条记录")

            # 更新所有数据和过滤数据
            self.all_memory_data = self.smart_memory.memory_data.copy()

            # 如果有搜索文本，重新应用搜索
            if hasattr(self, 'search_input') and self.search_input.text().strip():
                search_text = self.search_input.text().strip().lower()
                self.filtered_memory_data = self.perform_fuzzy_search(search_text)
                self.update_memory_table_with_filtered_data()
            else:
                self.filtered_memory_data = self.all_memory_data.copy()
                self.update_memory_table()

            print("✅ 记忆数据刷新完成")

        except Exception as e:
            error_msg = f"刷新记忆数据失败: {str(e)}"
            print(f"❌ {error_msg}")
            # 确保表格处于可用状态
            try:
                self.memory_table.clearContents()
                self.memory_table.setRowCount(0)
            except:
                pass
    

    
    def update_memory_table(self):
        """更新记忆表格（修复版）"""
        try:
            memory_data = self.smart_memory.memory_data

            # 清空表格并重置
            self.memory_table.clearContents()
            self.memory_table.setRowCount(0)

            # 验证记忆数据
            if not memory_data:
                print("📝 记忆数据为空，表格已清空")
                return

            # 过滤有效的记忆数据
            valid_records = []
            for memory_key, memory_record in memory_data.items():
                # 验证记忆记录的完整性
                if self.validate_memory_record(memory_key, memory_record):
                    valid_records.append((memory_key, memory_record))
                else:
                    print(f"⚠️ 跳过无效记忆记录: {memory_key}")

            # 设置表格行数
            self.memory_table.setRowCount(len(valid_records))

            # 先按款号排序记录，避免表格排序导致的问题
            valid_records.sort(key=lambda x: x[1].get("sku_code", ""))

            # 添加有效的记忆数据
            for row, (memory_key, memory_record) in enumerate(valid_records):
                # 确保行存在
                if row >= self.memory_table.rowCount():
                    print(f"⚠️ 行索引 {row} 超出表格范围，跳过")
                    continue

                try:
                    # 款号
                    sku_code = memory_record.get("sku_code", "")
                    if not sku_code:
                        sku_code = "未知款号"
                    self.memory_table.setItem(row, 0, QTableWidgetItem(str(sku_code)))

                    # 单价
                    unit_price = memory_record.get("unit_price", 0.0)
                    try:
                        price_text = f"¥{float(unit_price):.2f}"
                    except (ValueError, TypeError):
                        price_text = "¥0.00"
                    self.memory_table.setItem(row, 1, QTableWidgetItem(price_text))

                    # 选择颜色数
                    selected_count = memory_record.get("selected_color_count", 0)
                    try:
                        count_text = str(int(selected_count))
                    except (ValueError, TypeError):
                        count_text = "0"
                    self.memory_table.setItem(row, 2, QTableWidgetItem(count_text))

                    # 总颜色数
                    total_count = memory_record.get("total_color_count", 0)
                    try:
                        total_text = str(int(total_count))
                    except (ValueError, TypeError):
                        total_text = "0"
                    self.memory_table.setItem(row, 3, QTableWidgetItem(total_text))

                    # 已选择颜色规格详情（兼容新旧格式）
                    try:
                        # 优先使用新格式
                        selected_color_specs = memory_record.get("selected_color_specs", [])
                        if selected_color_specs:
                            selected_cards_detail = self.format_selected_color_specs_detail(selected_color_specs)
                        else:
                            # 兼容旧格式
                            selected_cards = memory_record.get("selected_cards", [])
                            if not isinstance(selected_cards, list):
                                selected_cards = []
                            selected_cards_detail = self.format_selected_cards_detail(selected_cards)

                        if not selected_cards_detail:
                            selected_cards_detail = "无"
                        detail_item = QTableWidgetItem(selected_cards_detail)
                        detail_item.setToolTip(selected_cards_detail)
                        self.memory_table.setItem(row, 4, detail_item)
                    except Exception as detail_error:
                        print(f"⚠️ 格式化颜色规格详情失败: {str(detail_error)}")
                        # 设置默认值
                        detail_item = QTableWidgetItem("格式化失败")
                        self.memory_table.setItem(row, 4, detail_item)

                    # 验证所有列都已设置
                    for col in range(5):
                        if not self.memory_table.item(row, col):
                            print(f"⚠️ 行 {row} 列 {col} 为空，设置默认值")
                            self.memory_table.setItem(row, col, QTableWidgetItem("空"))

                except Exception as row_error:
                    print(f"⚠️ 添加表格行 {row} 失败: {str(row_error)}")
                    # 确保所有列都有值，避免空白单元格
                    for col in range(5):
                        if not self.memory_table.item(row, col):
                            self.memory_table.setItem(row, col, QTableWidgetItem("错误"))

            # 不再使用sortItems，因为已经预先排序了

            print(f"✅ 记忆表格更新完成，共 {len(valid_records)} 条有效记录")

        except Exception as e:
            print(f"❌ 更新记忆表格失败: {str(e)}")
            # 确保表格处于可用状态
            self.memory_table.clearContents()
            self.memory_table.setRowCount(0)

    def validate_memory_record(self, memory_key, memory_record):
        """验证记忆记录的完整性"""
        try:
            # 检查必要字段
            required_fields = ["sku_code", "unit_price", "selected_color_count", "total_color_count"]
            for field in required_fields:
                if field not in memory_record:
                    print(f"⚠️ 记忆记录缺少字段 {field}: {memory_key}")
                    return False

            # 检查数据类型
            sku_code = memory_record.get("sku_code")
            if not isinstance(sku_code, str) or not sku_code.strip():
                print(f"⚠️ 记忆记录款号无效: {memory_key}")
                return False

            unit_price = memory_record.get("unit_price")
            if not isinstance(unit_price, (int, float)) or unit_price < 0:
                print(f"⚠️ 记忆记录单价无效: {memory_key}")
                return False

            return True

        except Exception as e:
            print(f"⚠️ 验证记忆记录时出错: {memory_key} - {str(e)}")
            return False

    def format_selected_color_specs_detail(self, selected_color_specs):
        """
        格式化已选择颜色规格的详细信息（新格式）
        显示颜色规格组信息，格式：DSD007-1096-蓝色

        Args:
            selected_color_specs: 选择的颜色规格列表

        Returns:
            格式化后的详细信息字符串
        """
        if not selected_color_specs:
            return "无"

        display_items = []
        for color_spec in selected_color_specs:
            display_sku = color_spec.get("display_sku", "")
            if display_sku:
                display_items.append(display_sku)
            else:
                # 备选方案：使用颜色名称
                color_name = color_spec.get("color_name", "")
                if color_name:
                    display_items.append(color_name)

        # 去重并排序
        unique_items = sorted(list(set(display_items)))

        # 多个颜色规格组用 | 分隔
        return " | ".join(unique_items) if unique_items else "无"

    def format_selected_cards_detail(self, selected_cards):
        """
        格式化已选择颜色规格的详细信息（旧格式兼容）
        只显示颜色规格组信息，格式：DSD007-1096-蓝色

        Args:
            selected_cards: 选择的商品卡列表

        Returns:
            格式化后的详细信息字符串
        """
        if not selected_cards:
            return "无"

        # 使用集合去重，避免重复显示相同的颜色规格组
        color_groups = set()

        for card in selected_cards:
            # 获取SKU ID
            sku_id = card.get("sku_id", "")
            if sku_id:
                # 提取颜色规格组信息（去掉尺码部分）
                # 例如：DSD007-1096-蓝色-L -> DSD007-1096-蓝色
                color_group = self.extract_color_group_from_sku(sku_id)
                if color_group:
                    color_groups.add(color_group)

        # 如果没有有效的颜色规格组，使用颜色名称作为备选
        if not color_groups:
            for card in selected_cards:
                color_name = card.get("color_name", "")
                if color_name:
                    color_groups.add(color_name)

        # 转换为列表并排序，确保显示顺序一致
        sorted_groups = sorted(list(color_groups))

        # 多个颜色规格组用 | 分隔
        return " | ".join(sorted_groups) if sorted_groups else "无"

    def extract_color_group_from_sku(self, sku_id):
        """
        从SKU ID中提取颜色规格组信息（去掉尺码）

        Args:
            sku_id: SKU ID，如 DSD007-1096-蓝色-L

        Returns:
            颜色规格组，如 DSD007-1096-蓝色
        """
        if not sku_id:
            return ""

        # 按照 - 分割SKU ID
        parts = sku_id.split("-")

        # 如果有4个或更多部分，去掉最后一个（通常是尺码）
        if len(parts) >= 4:
            # 重新组合前面的部分（去掉尺码）
            color_group = "-".join(parts[:-1])
            return color_group
        elif len(parts) >= 3:
            # 如果只有3个部分，直接返回（可能没有尺码）
            return sku_id
        else:
            # 如果部分太少，直接返回原始SKU ID
            return sku_id

    def add_memory_rule(self):
        """添加新的智能记忆规则"""
        try:
            dialog = AddMemoryDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                rule_data = dialog.get_rule_data()

                # 生成记忆键
                memory_key = self.smart_memory.generate_memory_key(
                    rule_data['sku_code'],
                    rule_data['unit_price']
                )

                # 检查是否已存在
                if memory_key in self.smart_memory.memory_data:
                    QMessageBox.warning(self, "警告", f"款号 {rule_data['sku_code']} 单价 {rule_data['unit_price']} 的记忆已存在！")
                    return

                # 创建记忆记录
                memory_record = {
                    "sku_code": rule_data['sku_code'],
                    "unit_price": rule_data['unit_price'],
                    "selected_cards": rule_data['selected_cards'],
                    "total_color_count": rule_data['total_color_count'],
                    "selected_color_count": len(rule_data['selected_cards']),
                    "created_time": datetime.now().isoformat(),
                    "last_used_time": datetime.now().isoformat(),
                    "use_count": 0
                }

                # 添加到记忆数据
                self.smart_memory.memory_data[memory_key] = memory_record

                # 保存数据
                success = self.smart_memory.save_memory_data()

                if success:
                    QMessageBox.information(self, "成功", "智能记忆规则添加成功！")
                    self.refresh_memory_data()
                else:
                    QMessageBox.warning(self, "失败", "保存记忆规则失败！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加记忆规则时出错: {str(e)}")

    def delete_selected_memory(self):
        """删除选中的记忆规则（支持单选和多选）"""
        try:
            # 获取所有选中的行
            selected_rows = []
            for i in range(self.memory_table.rowCount()):
                if self.memory_table.item(i, 0) and self.memory_table.item(i, 0).isSelected():
                    selected_rows.append(i)

            # 如果没有通过单元格选择，尝试获取当前行
            if not selected_rows:
                current_row = self.memory_table.currentRow()
                if current_row >= 0:
                    selected_rows = [current_row]

            # 如果还是没有选中的行，提示用户
            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要删除的记忆规则！\n可以点击行号选择单行，或按住Ctrl键选择多行。")
                return

            # 收集要删除的记忆信息
            memories_to_delete = []
            invalid_rows = []

            for row in selected_rows:
                # 安全获取单元格数据
                sku_item = self.memory_table.item(row, 0)
                price_item = self.memory_table.item(row, 1)

                # 检查单元格是否为空
                if not sku_item or not price_item:
                    invalid_rows.append(row + 1)  # 显示行号从1开始
                    continue

                sku_code = sku_item.text().strip()
                unit_price_text = price_item.text().strip()

                # 检查数据是否有效
                if not sku_code or not unit_price_text:
                    invalid_rows.append(row + 1)
                    continue

                try:
                    unit_price = float(unit_price_text.replace('¥', ''))
                    memory_key = self.smart_memory.generate_memory_key(sku_code, unit_price)
                    memories_to_delete.append({
                        'row': row + 1,
                        'sku_code': sku_code,
                        'unit_price': unit_price,
                        'unit_price_text': unit_price_text,
                        'memory_key': memory_key
                    })
                except ValueError:
                    invalid_rows.append(row + 1)
                    continue

            # 如果有无效行，提示用户
            if invalid_rows:
                QMessageBox.warning(
                    self,
                    "数据错误",
                    f"第 {', '.join(map(str, invalid_rows))} 行数据不完整或格式错误，无法删除！\n请点击刷新按钮修复表格显示。"
                )
                if not memories_to_delete:  # 如果没有有效的记忆可删除，直接返回
                    return

            # 如果没有有效的记忆可删除
            if not memories_to_delete:
                QMessageBox.warning(self, "错误", "没有有效的记忆规则可以删除！")
                return

            # 构建确认删除的消息
            if len(memories_to_delete) == 1:
                memory = memories_to_delete[0]
                confirm_msg = f"确定要删除款号 {memory['sku_code']} 单价 {memory['unit_price_text']} 的记忆规则吗？"
            else:
                memory_list = []
                for memory in memories_to_delete[:5]:  # 最多显示5个
                    memory_list.append(f"• {memory['sku_code']} ({memory['unit_price_text']})")

                if len(memories_to_delete) > 5:
                    memory_list.append(f"• ... 还有 {len(memories_to_delete) - 5} 条记录")

                confirm_msg = f"确定要删除以下 {len(memories_to_delete)} 条记忆规则吗？\n\n" + "\n".join(memory_list)

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                confirm_msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 执行删除操作
                deleted_count = 0
                not_found_count = 0

                for memory in memories_to_delete:
                    if memory['memory_key'] in self.smart_memory.memory_data:
                        del self.smart_memory.memory_data[memory['memory_key']]
                        deleted_count += 1
                    else:
                        not_found_count += 1

                # 保存数据
                success = self.smart_memory.save_memory_data()

                if success:
                    # 构建成功消息
                    if deleted_count > 0:
                        success_msg = f"成功删除 {deleted_count} 条记忆规则！"
                        if not_found_count > 0:
                            success_msg += f"\n（{not_found_count} 条记录未找到对应的记忆规则）"
                        QMessageBox.information(self, "删除成功", success_msg)
                    else:
                        QMessageBox.warning(self, "警告", "未找到对应的记忆规则！")

                    # 刷新表格
                    self.refresh_memory_data()
                else:
                    QMessageBox.warning(self, "失败", "保存数据失败！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除记忆规则时出错: {str(e)}")

    def refresh_table_manually(self):
        """手动刷新表格"""
        try:
            print("🔄 手动刷新表格开始...")

            # 重新加载记忆数据
            self.smart_memory.load_memory_data()

            # 更新表格显示
            self.update_memory_table()

            # 显示成功消息
            QMessageBox.information(self, "成功", "表格刷新完成！")
            print("✅ 手动刷新表格完成")

        except Exception as e:
            error_msg = f"刷新表格失败: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "错误", error_msg)

    def import_memory_data(self):
        """从JSON文件导入智能记忆规则"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "导入智能记忆数据",
                "",
                "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    import_data = json.load(f)

                # 验证数据格式
                if not isinstance(import_data, dict):
                    QMessageBox.warning(self, "错误", "导入文件格式不正确！")
                    return

                # 统计导入信息
                imported_count = 0
                skipped_count = 0

                for memory_key, memory_record in import_data.items():
                    if memory_key in self.smart_memory.memory_data:
                        skipped_count += 1
                    else:
                        self.smart_memory.memory_data[memory_key] = memory_record
                        imported_count += 1

                # 保存数据
                success = self.smart_memory.save_memory_data()

                if success:
                    QMessageBox.information(
                        self,
                        "导入完成",
                        f"成功导入 {imported_count} 条记忆规则\n跳过重复 {skipped_count} 条"
                    )
                    self.refresh_memory_data()
                else:
                    QMessageBox.warning(self, "失败", "保存导入数据失败！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入记忆数据时出错: {str(e)}")
    
    def export_memory_data(self):
        """导出记忆数据"""
        try:
            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出智能记忆数据",
                f"smart_memory_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON文件 (*.json)"
            )

            if file_path:
                # 直接导出记忆数据（不包含元数据）
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.smart_memory.memory_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"记忆数据已导出到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出记忆数据时出错: {str(e)}")

    def on_search_text_changed(self, text):
        """搜索文本改变时的处理"""
        try:
            search_text = text.strip().lower()

            if not search_text:
                # 如果搜索框为空，显示所有数据
                self.filtered_memory_data = self.all_memory_data.copy()
            else:
                # 执行模糊搜索
                self.filtered_memory_data = self.perform_fuzzy_search(search_text)

            # 更新表格显示
            self.update_memory_table_with_filtered_data()

        except Exception as e:
            print(f"❌ 搜索处理失败: {str(e)}")

    def perform_fuzzy_search(self, search_text):
        """执行模糊搜索"""
        try:
            filtered_data = {}

            for memory_key, memory_record in self.all_memory_data.items():
                # 搜索字段列表
                search_fields = []

                # 款号
                sku_code = memory_record.get("sku_code", "").lower()
                search_fields.append(sku_code)

                # 单价
                unit_price = memory_record.get("unit_price", 0.0)
                price_text = f"{unit_price:.2f}"
                search_fields.append(price_text)

                # 颜色规格详情（兼容新旧格式）
                selected_color_specs = memory_record.get("selected_color_specs", [])
                if selected_color_specs:
                    # 新格式
                    color_details = self.format_selected_color_specs_detail(selected_color_specs).lower()
                    search_fields.append(color_details)

                    # 从selected_color_specs中提取更多搜索字段
                    for spec in selected_color_specs:
                        search_fields.append(spec.get("color_name", "").lower())
                        search_fields.append(spec.get("color_spec", "").lower())
                        search_fields.append(spec.get("display_sku", "").lower())
                else:
                    # 旧格式兼容
                    selected_cards = memory_record.get("selected_cards", [])
                    color_details = self.format_selected_cards_detail(selected_cards).lower()
                    search_fields.append(color_details)

                    # 从selected_cards中提取更多搜索字段
                    for card in selected_cards:
                        search_fields.append(card.get("color_name", "").lower())
                        search_fields.append(card.get("sku_id", "").lower())
                        search_fields.append(card.get("sku_name", "").lower())
                        search_fields.append(card.get("color_spec", "").lower())
                        search_fields.append(card.get("size_spec", "").lower())

                # 检查是否匹配搜索文本
                if self.is_match(search_text, search_fields):
                    filtered_data[memory_key] = memory_record

            return filtered_data

        except Exception as e:
            print(f"❌ 模糊搜索失败: {str(e)}")
            return self.all_memory_data.copy()

    def is_match(self, search_text, search_fields):
        """检查搜索文本是否匹配任何字段"""
        try:
            # 支持多个关键词搜索（空格分隔）
            search_keywords = search_text.split()

            for keyword in search_keywords:
                keyword_found = False
                for field in search_fields:
                    if keyword in str(field):
                        keyword_found = True
                        break

                # 如果任何一个关键词都没找到，则不匹配
                if not keyword_found:
                    return False

            # 所有关键词都找到了
            return True

        except Exception as e:
            print(f"❌ 匹配检查失败: {str(e)}")
            return False

    def clear_search(self):
        """清空搜索"""
        try:
            self.search_input.clear()
            self.filtered_memory_data = self.all_memory_data.copy()
            self.update_memory_table_with_filtered_data()

        except Exception as e:
            print(f"❌ 清空搜索失败: {str(e)}")

    def update_memory_table_with_filtered_data(self):
        """使用过滤后的数据更新表格"""
        try:
            # 临时替换smart_memory的数据
            original_data = self.smart_memory.memory_data
            self.smart_memory.memory_data = self.filtered_memory_data

            # 更新表格
            self.update_memory_table()

            # 恢复原始数据
            self.smart_memory.memory_data = original_data

        except Exception as e:
            print(f"❌ 更新过滤表格失败: {str(e)}")


class AddMemoryDialog(QDialog):
    """添加记忆规则对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加智能记忆规则")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()
        self.setup_dark_theme()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 表单布局
        form_layout = QFormLayout()

        # 款号输入
        self.sku_code_edit = QLineEdit()
        self.sku_code_edit.setPlaceholderText("例如：1100")
        form_layout.addRow("款号:", self.sku_code_edit)

        # 单价输入
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0.01, 99999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" 元")
        form_layout.addRow("单价:", self.unit_price_spin)

        # 总颜色数
        self.total_colors_spin = QDoubleSpinBox()
        self.total_colors_spin.setRange(2, 20)
        self.total_colors_spin.setDecimals(0)
        self.total_colors_spin.setValue(3)
        form_layout.addRow("总颜色数:", self.total_colors_spin)

        layout.addLayout(form_layout)

        # 选择的商品卡信息
        layout.addWidget(QLabel("选择的商品卡信息:"))
        self.cards_text = QTextEdit()
        self.cards_text.setPlaceholderText(
            "请输入JSON格式的商品卡信息，例如:\n"
            "[\n"
            "  {\n"
            "    \"color_name\": \"红色\",\n"
            "    \"sku_id\": \"DSD0070-1100-红色-L\",\n"
            "    \"sku_name\": \"DSD0070-1100-两件套-红色-L\",\n"
            "    \"color_spec\": \"红色\",\n"
            "    \"size_spec\": \"L\",\n"
            "    \"current_price\": 120.0\n"
            "  }\n"
            "]"
        )
        layout.addWidget(self.cards_text)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def setup_dark_theme(self):
        """设置深色主题"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
            }

            QLineEdit, QDoubleSpinBox, QTextEdit {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #ffffff;
            }

            QLabel {
                color: #ffffff;
            }

            QDialogButtonBox QPushButton {
                background-color: #6a4c93;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }

            QDialogButtonBox QPushButton:hover {
                background-color: #7b5aa6;
            }
        """)

    def get_rule_data(self):
        """获取规则数据"""
        try:
            # 解析商品卡JSON
            cards_json = self.cards_text.toPlainText().strip()
            if not cards_json:
                raise ValueError("请输入商品卡信息")

            selected_cards = json.loads(cards_json)
            if not isinstance(selected_cards, list) or not selected_cards:
                raise ValueError("商品卡信息格式不正确")

            return {
                'sku_code': self.sku_code_edit.text().strip(),
                'unit_price': self.unit_price_spin.value(),
                'total_color_count': int(self.total_colors_spin.value()),
                'selected_cards': selected_cards
            }
        except json.JSONDecodeError:
            raise ValueError("商品卡信息JSON格式不正确")
        except Exception as e:
            raise ValueError(f"数据验证失败: {str(e)}")

    def accept(self):
        """确认对话框"""
        try:
            rule_data = self.get_rule_data()

            # 验证必填字段
            if not rule_data['sku_code']:
                QMessageBox.warning(self, "错误", "请输入款号！")
                return

            if rule_data['unit_price'] <= 0:
                QMessageBox.warning(self, "错误", "请输入有效的单价！")
                return

            super().accept()

        except ValueError as e:
            QMessageBox.warning(self, "错误", str(e))
