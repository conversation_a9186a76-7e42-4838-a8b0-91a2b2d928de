#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆管理器 - 自动记忆用户的颜色规格选择
根据款号+单价+颜色规格进行智能匹配和记忆（优化版）
"""

import json
import os
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime


class SmartMemoryManager:
    """智能记忆管理器（优化版）"""

    def __init__(self, memory_file: str = "smart_memory.json"):
        """
        初始化智能记忆管理器

        Args:
            memory_file: 记忆数据文件路径
        """
        self.memory_file = memory_file
        self.memory_data = {}
        self.pending_memories = {}  # 待保存的记忆数据
        self.load_memory_data()

    def load_memory_data(self):
        """加载记忆数据并自动迁移旧格式"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)

                # 检查是否需要数据迁移
                migrated_data = self._migrate_old_format_if_needed(raw_data)

                if migrated_data != raw_data:
                    print("🔄 检测到旧格式数据，正在自动迁移...")
                    self.memory_data = migrated_data
                    self._backup_and_save_migrated_data()
                else:
                    self.memory_data = raw_data

                print(f"✅ 智能记忆数据加载成功，共 {len(self.memory_data)} 条记录")
            else:
                self.memory_data = {}
                print("📝 智能记忆数据文件不存在，创建新的记忆库")
        except Exception as e:
            print(f"❌ 加载智能记忆数据失败: {str(e)}")
            self.memory_data = {}
    
    def _migrate_old_format_if_needed(self, raw_data: Dict) -> Dict:
        """
        检查并迁移旧格式数据到新格式

        Args:
            raw_data: 原始数据

        Returns:
            迁移后的数据
        """
        migrated_data = {}
        migration_count = 0

        for memory_key, memory_record in raw_data.items():
            if "selected_cards" in memory_record:
                # 旧格式：包含selected_cards
                migrated_record = self._convert_old_record_to_new_format(memory_record)
                migrated_data[memory_key] = migrated_record
                migration_count += 1
            else:
                # 新格式：直接复制
                migrated_data[memory_key] = memory_record

        if migration_count > 0:
            print(f"🔄 成功迁移 {migration_count} 条旧格式记录")

        return migrated_data

    def _convert_old_record_to_new_format(self, old_record: Dict) -> Dict:
        """
        将旧格式记录转换为新格式

        Args:
            old_record: 旧格式记录

        Returns:
            新格式记录
        """
        selected_cards = old_record.get("selected_cards", [])

        # 提取颜色规格信息（去重）
        color_specs = {}
        for card in selected_cards:
            color_name = card.get("color_name", "")
            if color_name and color_name not in color_specs:
                sku_id = card.get("sku_id", "")
                display_sku = self._extract_display_sku_from_card(sku_id, color_name)

                color_specs[color_name] = {
                    "color_name": color_name,
                    "color_spec": card.get("color_spec", color_name),
                    "display_sku": display_sku
                }

        # 构建新格式记录
        new_record = {
            "sku_code": old_record.get("sku_code", ""),
            "unit_price": old_record.get("unit_price", 0.0),
            "selected_color_specs": list(color_specs.values()),
            "total_color_count": old_record.get("total_color_count", 0),
            "selected_color_count": len(color_specs),
            "created_time": old_record.get("created_time", datetime.now().isoformat()),
            "last_used_time": old_record.get("last_used_time", datetime.now().isoformat()),
            "use_count": old_record.get("use_count", 0)
        }

        return new_record

    def _extract_display_sku_from_card(self, sku_id: str, color_name: str) -> str:
        """
        从卡片信息中提取用于显示的SKU格式

        Args:
            sku_id: SKU ID，如 "DSD007-1100-蓝色-L"
            color_name: 颜色名称

        Returns:
            显示用SKU，如 "DSD007-1100-蓝色"
        """
        if not sku_id:
            return color_name

        # 按-分割，去掉最后的尺码部分
        parts = sku_id.split("-")
        if len(parts) >= 4:
            # DSD007-1100-蓝色-L -> DSD007-1100-蓝色
            return "-".join(parts[:-1])
        elif len(parts) >= 3:
            # DSD007-1100-蓝色 -> DSD007-1100-蓝色
            return sku_id
        else:
            return color_name

    def _backup_and_save_migrated_data(self):
        """备份原始数据并保存迁移后的数据"""
        try:
            # 备份原始文件
            backup_file = f"{self.memory_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if os.path.exists(self.memory_file):
                import shutil
                shutil.copy2(self.memory_file, backup_file)
                print(f"📦 原始数据已备份到: {backup_file}")

            # 保存迁移后的数据
            self.save_memory_data()
            print("✅ 数据迁移完成")
        except Exception as e:
            print(f"❌ 数据迁移保存失败: {str(e)}")

    def save_memory_data(self):
        """保存记忆数据到文件"""
        try:
            # 合并待保存的记忆数据
            if self.pending_memories:
                self.memory_data.update(self.pending_memories)
                self.pending_memories.clear()

            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 智能记忆数据保存成功，共 {len(self.memory_data)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 保存智能记忆数据失败: {str(e)}")
            return False
    
    def generate_memory_key(self, sku_code: str, unit_price: float) -> str:
        """
        生成记忆键
        
        Args:
            sku_code: 款号
            unit_price: 单价
            
        Returns:
            记忆键字符串
        """
        return f"{sku_code}|{unit_price:.2f}"

    def extract_core_sku_code(self, sku_code: str) -> str:
        """
        提取核心款号（去掉供应商前缀）
        格式：供应商-款号 → 款号

        Args:
            sku_code: 原始款号，如 "SDSD007-1100" 或 "1100"

        Returns:
            核心款号，如 "1100"
        """
        if not sku_code:
            return sku_code

        # 使用正则表达式提取第一个-符号后面的内容
        match = re.search(r'-(.+)', sku_code)
        if match:
            core_sku = match.group(1)
            print(f"🔍 提取核心款号: {sku_code} → {core_sku}")
            return core_sku

        # 如果没有-符号，返回原始款号
        return sku_code

    def should_remember(self, color_groups: Dict[str, List[Dict]]) -> bool:
        """
        判断是否需要记忆（只对多颜色规格的商品进行记忆）
        
        Args:
            color_groups: 颜色组数据
            
        Returns:
            是否需要记忆
        """
        return len(color_groups) > 1
    
    def extract_color_spec_info(self, selected_color_groups: Dict[str, List[Dict]]) -> List[Dict]:
        """
        提取颜色规格信息用于记忆（优化版）

        Args:
            selected_color_groups: 选中的颜色组

        Returns:
            颜色规格信息列表
        """
        color_specs = []
        for color_name, skus in selected_color_groups.items():
            if skus:
                # 从第一个SKU提取显示用的SKU前缀
                first_sku = skus[0]
                sku_id = first_sku.get("sku_id", "")
                display_sku = self._extract_display_sku(sku_id, color_name)

                color_specs.append({
                    "color_name": color_name,
                    "color_spec": first_sku.get("color_spec", color_name),
                    "display_sku": display_sku
                })
        return color_specs

    def _extract_display_sku(self, sku_id: str, color_name: str) -> str:
        """
        提取用于显示的SKU格式：DSD007-1100-蓝色

        Args:
            sku_id: 完整SKU ID
            color_name: 颜色名称

        Returns:
            显示用SKU
        """
        if not sku_id:
            return color_name

        # 按-分割，去掉最后的尺码部分
        parts = sku_id.split("-")
        if len(parts) >= 4:
            # DSD007-1100-蓝色-L -> DSD007-1100-蓝色
            return "-".join(parts[:-1])
        elif len(parts) >= 3:
            # DSD007-1100-蓝色 -> DSD007-1100-蓝色
            return sku_id
        else:
            return color_name
    
    def add_pending_memory(self, sku_code: str, unit_price: float,
                          selected_color_groups: Dict[str, List[Dict]],
                          all_color_groups: Dict[str, List[Dict]]):
        """
        添加待保存的记忆（只有在成本价上传成功后才真正保存）

        Args:
            sku_code: 款号（可能包含供应商前缀，如DSD007-1596）
            unit_price: 单价
            selected_color_groups: 选中的颜色组
            all_color_groups: 所有颜色组
        """
        # 只对多颜色规格的商品进行记忆
        if not self.should_remember(all_color_groups):
            print(f"🔍 款号 {sku_code} 只有单个颜色规格，无需记忆")
            return

        # 提取核心SKU（去掉供应商前缀）
        core_sku = self.extract_core_sku_code(sku_code)
        print(f"🔍 智能记忆保存：原始SKU={sku_code} → 核心SKU={core_sku}")

        # 使用核心SKU生成记忆键
        memory_key = self.generate_memory_key(core_sku, unit_price)

        # 检查是否已存在相同的记忆（使用核心SKU检查）
        if memory_key in self.memory_data:
            print(f"🔍 款号 {core_sku} 单价 {unit_price} 的记忆已存在，跳过")
            return

        # 提取颜色规格信息（优化版）
        color_spec_info = self.extract_color_spec_info(selected_color_groups)

        # 添加到待保存记忆（保存核心SKU）
        memory_record = {
            "sku_code": core_sku,  # 保存核心SKU而不是原始SKU
            "unit_price": unit_price,
            "selected_color_specs": color_spec_info,  # 🔥 新格式：颜色规格信息
            "total_color_count": len(all_color_groups),
            "selected_color_count": len(selected_color_groups),
            "created_time": datetime.now().isoformat(),
            "last_used_time": datetime.now().isoformat(),
            "use_count": 1
        }

        self.pending_memories[memory_key] = memory_record
        print(f"📝 添加待保存记忆: 原始SKU={sku_code}, 核心SKU={core_sku}, 单价={unit_price}, 选择 {len(selected_color_groups)} 个颜色")
    
    def find_matching_memory(self, sku_code: str, unit_price: float,
                           available_color_groups: Dict[str, List[Dict]]) -> Optional[Dict]:
        """
        查找匹配的记忆（优化版）

        Args:
            sku_code: 款号（可能包含供应商前缀，如DSD007-1596）
            unit_price: 单价
            available_color_groups: 当前可用的颜色组

        Returns:
            匹配的记忆记录，如果没有匹配则返回None
        """
        # 只对多颜色规格的商品进行匹配
        if not self.should_remember(available_color_groups):
            return None

        # 直接使用核心SKU进行匹配（因为记忆都是用核心SKU保存的）
        core_sku = self.extract_core_sku_code(sku_code)
        print(f"🔍 智能记忆查找：原始SKU={sku_code} → 核心SKU={core_sku}")

        # 使用核心SKU生成记忆键
        memory_key = self.generate_memory_key(core_sku, unit_price)

        # 先检查已保存的记忆
        memory_record = self.memory_data.get(memory_key)
        if not memory_record:
            # 再检查待保存的记忆
            memory_record = self.pending_memories.get(memory_key)

        if not memory_record:
            print(f"💭 未找到匹配记忆: 核心SKU={core_sku}, 单价={unit_price}")
            return None

        # 简化的匹配：只检查颜色名称
        selected_color_specs = memory_record.get("selected_color_specs", [])
        available_colors = list(available_color_groups.keys())

        # 提取记忆中的颜色名称
        memory_colors = [spec["color_name"] for spec in selected_color_specs]

        # 检查记忆中的颜色是否都在当前可用颜色中
        if all(color in available_colors for color in memory_colors):
            print(f"🎯 找到匹配记忆: 原始SKU={sku_code}, 核心SKU={core_sku}, 单价={unit_price}, 匹配 {len(memory_colors)} 个颜色")
            return {
                "memory_record": memory_record,
                "selected_colors": memory_colors
            }

        print(f"💭 记忆中的颜色在当前不可用: 记忆颜色={memory_colors}, 可用颜色={available_colors}")
        return None
    
    def convert_matching_colors_to_selection(self, selected_colors: List[str],
                                           available_color_groups: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """
        将匹配的颜色转换为选择格式（优化版）

        Args:
            selected_colors: 匹配的颜色列表
            available_color_groups: 可用的颜色组

        Returns:
            选择格式的颜色组字典
        """
        selection = {}
        for color_name in selected_colors:
            if color_name in available_color_groups:
                selection[color_name] = available_color_groups[color_name]
        return selection
    
    def convert_matching_cards_to_selection(self, matching_cards: List[Dict]) -> Dict[str, List[Dict]]:
        """
        将匹配的卡片转换为选择格式
        
        Args:
            matching_cards: 匹配的卡片列表
            
        Returns:
            选择格式的颜色组字典
        """
        selection = {}
        for card in matching_cards:
            color_name = card["color_name"]
            if color_name not in selection:
                selection[color_name] = []
            selection[color_name].append(card)
        return selection
    
    def commit_pending_memories(self):
        """提交待保存的记忆（在成本价上传成功后调用）"""
        if self.pending_memories:
            committed_count = len(self.pending_memories)
            success = self.save_memory_data()
            if success:
                print(f"✅ 成功提交 {committed_count} 条智能记忆")
            return success
        return True
    
    def clear_pending_memories(self):
        """清除待保存的记忆（在操作失败时调用）"""
        if self.pending_memories:
            cleared_count = len(self.pending_memories)
            self.pending_memories.clear()
            print(f"🗑️ 清除 {cleared_count} 条待保存记忆")
    
    def update_memory_usage(self, sku_code: str, unit_price: float):
        """更新记忆使用统计"""
        # 🔥 关键修复：使用核心SKU生成记忆键
        core_sku = self.extract_core_sku_code(sku_code)
        memory_key = self.generate_memory_key(core_sku, unit_price)
        if memory_key in self.memory_data:
            self.memory_data[memory_key]["last_used_time"] = datetime.now().isoformat()
            self.memory_data[memory_key]["use_count"] = self.memory_data[memory_key].get("use_count", 0) + 1
            print(f"📊 更新记忆使用统计: 原始SKU={sku_code}, 核心SKU={core_sku}, 使用次数={self.memory_data[memory_key]['use_count']}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        return {
            "total_memories": len(self.memory_data),
            "pending_memories": len(self.pending_memories),
            "memory_file": self.memory_file,
            "file_exists": os.path.exists(self.memory_file)
        }
