#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI配置预设管理器
负责AI配置预设的保存、加载、管理
"""

import json
import os
import time
from typing import Dict, List, Optional
from datetime import datetime

class AIConfigPresetManager:
    """AI配置预设管理器"""
    
    def __init__(self, preset_file: str = "ai_config_presets.json"):
        self.preset_file = preset_file
        self.presets = []
        self.load_presets()
    
    def load_presets(self) -> List[Dict]:
        """加载预设配置"""
        try:
            if os.path.exists(self.preset_file):
                with open(self.preset_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.presets = data.get("presets", [])
            else:
                self.presets = self.get_default_presets()
                self.save_presets()
            return self.presets
        except Exception as e:
            print(f"加载AI配置预设失败: {str(e)}")
            self.presets = self.get_default_presets()
            return self.presets
    
    def save_presets(self) -> bool:
        """保存预设配置"""
        try:
            data = {
                "version": "1.0",
                "updated_time": time.time(),
                "presets": self.presets
            }
            with open(self.preset_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存AI配置预设失败: {str(e)}")
            return False
    
    def get_default_presets(self) -> List[Dict]:
        """获取默认预设配置"""
        current_time = time.time()
        return [
            {
                "name": "OpenAI GPT-4",
                "config": {
                    "api_key": "",
                    "api_url": "https://api.openai.com/v1/chat/completions",
                    "model_name": "gpt-4",
                    "temperature": 70,
                    "max_tokens": 4000,
                    "timeout": 60
                },
                "created_time": current_time,
                "last_used": None
            },
            {
                "name": "OpenAI GPT-3.5",
                "config": {
                    "api_key": "",
                    "api_url": "https://api.openai.com/v1/chat/completions",
                    "model_name": "gpt-3.5-turbo",
                    "temperature": 70,
                    "max_tokens": 4000,
                    "timeout": 60
                },
                "created_time": current_time,
                "last_used": None
            },
            {
                "name": "Claude-3",
                "config": {
                    "api_key": "",
                    "api_url": "https://api.anthropic.com/v1/messages",
                    "model_name": "claude-3-sonnet-20240229",
                    "temperature": 70,
                    "max_tokens": 4000,
                    "timeout": 60
                },
                "created_time": current_time,
                "last_used": None
            }
        ]
    
    def add_preset(self, name: str, config: Dict) -> bool:
        """添加新的预设配置"""
        try:
            # 检查名称是否已存在
            if self.get_preset_by_name(name):
                return False, "预设名称已存在"
            
            preset = {
                "name": name,
                "config": config.copy(),
                "created_time": time.time(),
                "last_used": None
            }
            
            self.presets.append(preset)
            return self.save_presets(), "预设保存成功"
            
        except Exception as e:
            return False, f"保存预设失败: {str(e)}"
    
    def delete_preset(self, name: str) -> bool:
        """删除预设配置"""
        try:
            self.presets = [p for p in self.presets if p["name"] != name]
            return self.save_presets()
        except Exception as e:
            print(f"删除预设失败: {str(e)}")
            return False
    
    def update_preset(self, old_name: str, new_name: str, config: Dict) -> bool:
        """更新预设配置"""
        try:
            for preset in self.presets:
                if preset["name"] == old_name:
                    preset["name"] = new_name
                    preset["config"] = config.copy()
                    preset["updated_time"] = time.time()
                    return self.save_presets()
            return False
        except Exception as e:
            print(f"更新预设失败: {str(e)}")
            return False
    
    def get_preset_by_name(self, name: str) -> Optional[Dict]:
        """根据名称获取预设配置"""
        for preset in self.presets:
            if preset["name"] == name:
                return preset
        return None
    
    def get_all_presets(self) -> List[Dict]:
        """获取所有预设配置"""
        return self.presets.copy()
    
    def mark_preset_used(self, name: str):
        """标记预设为已使用"""
        try:
            for preset in self.presets:
                if preset["name"] == name:
                    preset["last_used"] = time.time()
                    self.save_presets()
                    break
        except Exception as e:
            print(f"标记预设使用失败: {str(e)}")
    
    def export_presets(self, export_file: str) -> bool:
        """导出预设配置"""
        try:
            data = {
                "version": "1.0",
                "exported_time": time.time(),
                "presets": self.presets
            }
            with open(export_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出预设失败: {str(e)}")
            return False
    
    def import_presets(self, import_file: str, merge: bool = True) -> bool:
        """导入预设配置"""
        try:
            with open(import_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                imported_presets = data.get("presets", [])
            
            if merge:
                # 合并模式：添加不存在的预设
                existing_names = {p["name"] for p in self.presets}
                for preset in imported_presets:
                    if preset["name"] not in existing_names:
                        self.presets.append(preset)
            else:
                # 替换模式：完全替换
                self.presets = imported_presets
            
            return self.save_presets()
            
        except Exception as e:
            print(f"导入预设失败: {str(e)}")
            return False
    
    def get_preset_stats(self) -> Dict:
        """获取预设统计信息"""
        total_count = len(self.presets)
        used_count = len([p for p in self.presets if p.get("last_used")])
        
        return {
            "total_count": total_count,
            "used_count": used_count,
            "unused_count": total_count - used_count
        }
    
    def format_api_key_display(self, api_key: str) -> str:
        """格式化API密钥显示（隐藏中间部分）"""
        if not api_key:
            return "未设置"
        
        if len(api_key) <= 8:
            return "*" * len(api_key)
        
        # 显示前4位和后4位，中间用*号替代
        return f"{api_key[:4]}{'*' * (len(api_key) - 8)}{api_key[-4:]}"
    
    def format_time_display(self, timestamp: Optional[float]) -> str:
        """格式化时间显示"""
        if not timestamp:
            return "从未使用"
        
        try:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return "时间错误"
