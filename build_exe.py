#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - EXE打包脚本
使用PyInstaller将项目打包成独立的.exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    for spec_file in Path('.').glob('*.spec'):
        print(f"🧹 清理文件: {spec_file}")
        spec_file.unlink()

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件列表
datas = [
    ('config.json', '.'),
    ('user_config.json', '.'),
    ('suppliers.json', '.'),
    ('smart_memory.json', '.'),
    ('file_manager.json', '.'),
    ('latest_cookies.json', '.'),
    ('智能记忆功能说明.md', '.'),
    ('系统使用指南.md', '.'),
    ('项目完整文档.md', '.'),
    ('ERP_API_文档.md', '.'),
    ('modules', 'modules'),
]

# 隐藏导入（PyQt5相关）
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'requests',
    'pandas',
    'numpy',
    'PIL',
    'pdf2image',
    'loguru',
    'yaml',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智能票据处理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('智能票据处理系统.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 已创建spec文件: 智能票据处理系统.spec")

def build_exe():
    """执行打包"""
    print("🚀 开始打包...")
    
    # 使用spec文件打包
    cmd = [
        'pyinstaller',
        '--clean',
        '智能票据处理系统.spec'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        print(f"📁 输出目录: {os.path.abspath('dist')}")
        
        # 检查生成的文件
        exe_path = os.path.join('dist', '智能票据处理系统.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print(f"错误信息: {e.stderr}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 智能票据处理系统 - EXE打包工具")
    print("=" * 50)
    
    # 检查PyInstaller是否安装
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        print("✅ PyInstaller安装完成")
    
    # 清理旧文件
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 执行打包
    if build_exe():
        print("\n🎉 打包完成！")
        print("📁 可执行文件位置: dist/智能票据处理系统.exe")
        print("💡 提示: 可以将整个dist目录分发给用户")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
