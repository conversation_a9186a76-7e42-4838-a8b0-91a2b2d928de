#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 主启动文件 (PyQt5版本)
现代化界面设计，支持黑色主题和标签页布局
"""

import sys
import os
import warnings
import logging

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 🔇 全面抑制日志输出，只保留错误信息
warnings.filterwarnings("ignore")

# 🔇 设置全局日志级别为ERROR，抑制所有INFO和DEBUG日志
logging.getLogger().setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('requests').setLevel(logging.ERROR)

# 🔇 抑制PyQt5的调试输出
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false'

# 🔇 重定向标准输出，只保留错误输出
class ErrorOnlyOutput:
    """只允许错误信息输出的类"""
    def __init__(self, original_stdout):
        self.original_stdout = original_stdout

    def write(self, text):
        # 🔇 只允许包含错误标识的输出
        error_keywords = [
            '❌', 'error', 'exception', 'failed', 'fatal', 'critical',
            'traceback', 'importerror', 'modulenotfounderror',
            '缺少依赖包', '导入错误', '启动失败', '程序启动失败',
            'qt致命错误', '按回车键退出'
        ]

        if any(keyword in text.lower() for keyword in error_keywords):
            self.original_stdout.write(text)
        # 🔇 静默忽略其他所有输出（包括调试信息、状态信息等）

    def flush(self):
        self.original_stdout.flush()

# 🔇 启用严格的输出过滤器，只显示错误信息
sys.stdout = ErrorOnlyOutput(sys.stdout)

def check_dependencies():
    """静默检查依赖包 - 只在缺少依赖时显示错误"""
    required_packages = ['PyQt5', 'requests', 'PIL']

    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'PyQt5':
                import PyQt5
            else:
                __import__(package)
        except ImportError:
            # 只在真正缺少依赖时才显示错误信息
            print(f"❌ 缺少依赖包: {package}")
            print("请运行: pip install -r requirements.txt")
            return False

    return True

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入并启动PyQt5 GUI
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI

        # 🔇 设置Qt日志过滤器，只显示严重错误
        def qt_message_handler(mode, context, message):
            _ = context  # 忽略未使用的参数
            """Qt消息过滤器，只保留严重错误信息"""
            # 🔇 完全抑制所有Qt框架日志，只保留致命错误
            if mode == QtMsgType.QtFatalMsg:
                print(f"❌ Qt致命错误: {message}")

        # 安装消息处理器
        qInstallMessageHandler(qt_message_handler)

        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("智能票据处理系统")
        app.setApplicationVersion("2.0")

        # 创建并显示主窗口
        window = ModernTicketProcessorGUI()
        # 使用showMaximized()确保全屏显示，不使用show()避免覆盖全屏设置
        window.showMaximized()

        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
