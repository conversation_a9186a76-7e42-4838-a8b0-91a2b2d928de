#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 最终EXE打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    print("🎯 智能票据处理系统 - EXE打包工具")
    print("=" * 50)
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   清理目录: {dir_name}")
    
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"   清理文件: {spec_file}")
    
    # 检查图标
    icon_path = None
    for icon_name in ['combined.ico', '256x256.ico', '48x48.ico']:
        test_path = f"icon/{icon_name}"
        if os.path.exists(test_path):
            icon_path = test_path
            print(f"✅ 使用图标: {icon_path}")
            break
    
    if not icon_path:
        print("⚠️ 未找到图标文件")
    
    # 构建PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=智能票据处理系统',
        '--clean',
        '--noconfirm'
    ]
    
    # 添加图标
    if icon_path:
        cmd.extend(['--icon', icon_path])
    
    # 添加数据文件
    data_files = [
        'config.json',
        'user_config.json', 
        'suppliers.json',
        'smart_memory.json',
        'file_manager.json',
        'latest_cookies.json',
        'ai_config_presets.json',
        '智能记忆功能说明.md',
        '系统使用指南.md',
        '项目完整文档.md',
        'ERP_API_文档.md',
        'AI配置预设功能说明.md',
        'README.md'
    ]
    
    for data_file in data_files:
        if os.path.exists(data_file):
            cmd.extend(['--add-data', f'{data_file};.'])
    
    # 添加模块目录
    if os.path.exists('modules'):
        cmd.extend(['--add-data', 'modules;modules'])
    
    if os.path.exists('icon'):
        cmd.extend(['--add-data', 'icon;icon'])
    
    # 添加隐藏导入
    hidden_imports = [
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.sip',
        'requests',
        'pandas',
        'numpy',
        'PIL',
        'PIL.Image',
        'pdf2image',
        'loguru',
        'yaml'
    ]
    
    for module in hidden_imports:
        cmd.extend(['--hidden-import', module])
    
    # 添加主文件
    cmd.append('main.py')
    
    print("🚀 开始打包...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        
        # 检查生成的文件
        exe_path = 'dist/智能票据处理系统.exe'
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            print("🎉 打包完成！可以运行 dist/智能票据处理系统.exe")
        else:
            print("❌ 未找到生成的exe文件")
            
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print(f"错误: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
    except FileNotFoundError:
        print("❌ PyInstaller未安装，请先安装: pip install pyinstaller")

if __name__ == "__main__":
    main()
