#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 单包EXE打包脚本
使用PyInstaller将项目打包成单个独立的.exe文件
包含图标和所有必要资源
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    for spec_file in Path('.').glob('*.spec'):
        print(f"🧹 清理文件: {spec_file}")
        spec_file.unlink()

def check_icon_file():
    """检查图标文件"""
    icon_path = "icon/combined.ico"
    if os.path.exists(icon_path):
        print(f"✅ 找到图标文件: {icon_path}")
        return icon_path
    else:
        # 如果combined.ico不存在，尝试使用其他图标
        for icon_name in ["256x256.ico", "48x48.ico", "32x32.ico"]:
            icon_path = f"icon/{icon_name}"
            if os.path.exists(icon_path):
                print(f"✅ 使用备用图标: {icon_path}")
                return icon_path
        
        print("⚠️ 未找到图标文件，将不使用图标")
        return None

def create_single_exe_spec():
    """创建单包exe的spec文件"""
    icon_path = check_icon_file()
    icon_line = f"    icon='{icon_path}'," if icon_path else "    icon=None,"
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件列表 - 包含所有必要的配置和资源文件
datas = [
    ('config.json', '.'),
    ('user_config.json', '.'),
    ('suppliers.json', '.'),
    ('smart_memory.json', '.'),
    ('file_manager.json', '.'),
    ('latest_cookies.json', '.'),
    ('ai_config_presets.json', '.'),
    ('智能记忆功能说明.md', '.'),
    ('系统使用指南.md', '.'),
    ('项目完整文档.md', '.'),
    ('ERP_API_文档.md', '.'),
    ('AI配置预设功能说明.md', '.'),
    ('README.md', '.'),
    ('modules', 'modules'),
    ('icon', 'icon'),
]

# 隐藏导入 - 确保所有依赖都被正确包含
hiddenimports = [
    # PyQt5核心模块
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    
    # 网络和API
    'requests',
    'urllib3',
    'certifi',
    
    # 数据处理
    'pandas',
    'numpy',
    
    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'pdf2image',
    
    # 日志和配置
    'loguru',
    'yaml',
    'pyyaml',
    
    # 系统模块
    'json',
    'base64',
    'datetime',
    'time',
    'os',
    'sys',
    'threading',
    'pathlib',
    're',
    'typing',
    
    # 项目模块
    'modules.ai_processor',
    'modules.erp_integration',
    'modules.config_manager',
    'modules.file_manager',
    'modules.image_splitter',
    'modules.sku_matcher',
    'modules.smart_memory_manager',
    'modules.supplier_manager',
    'modules.table_manager',
    'modules.color_confirm_panel',
    'modules.ui_style_manager',
    'modules.ui_constants',
    'modules.return_rate_calculator',
    'modules.product_link_identifier',
    'modules.memory_settings_tab',
    'modules.ai_config_preset_manager',
    'modules.pyqt5_main_gui',
    
    # GUI模块
    'modules.gui',
    'modules.gui.main_gui_controller',
    'modules.gui.image_manager',
    'modules.gui.table_ui_manager',
    'modules.gui.ai_processor_ui',
    'modules.gui.config_ui_manager',
    'modules.gui.erp_ui_manager',
    'modules.gui.event_handlers',
    'modules.gui.image_card_widget',
    'modules.gui.cookies_manager_widget',
    'modules.gui.image_split_overlay',
    'modules.gui.ai_model_card',
    'modules.gui.preset_edit_dialog',
    
    # 线程模块
    'modules.threads',
    'modules.threads.ai_processing_thread',
    'modules.threads.erp_query_thread',
    'modules.threads.cost_update_thread',
    'modules.threads.thread_safe_logger',
    
    # Cookie模块
    'modules.cookies',
    'modules.cookies.manager',
    'modules.cookies.parser',
    'modules.cookies.validator',
    'modules.cookies.exceptions',
    
    # 工具模块
    'modules.utils',
    'modules.utils.logger',
]

# 排除不必要的模块以减小体积
excludes = [
    # 测试相关
    'pytest', 'unittest', 'test', 'tests',
    # 开发工具
    'pdb', 'pydoc', 'doctest', 'pydoc_data',
    # 不需要的GUI框架
    'tkinter', 'turtle', 'curses',
    # 不需要的科学计算库
    'matplotlib', 'scipy', 'sklearn', 'sympy',
    # 开发环境
    'jupyter', 'notebook', 'ipython', 'IPython',
    # PyQt5不需要的模块
    'PyQt5.QtWebEngine', 'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtQuick', 'PyQt5.QtQml', 'PyQt5.QtMultimedia',
    'PyQt5.QtOpenGL', 'PyQt5.QtSql', 'PyQt5.QtTest',
    'PyQt5.QtXml', 'PyQt5.QtXmlPatterns', 'PyQt5.QtDesigner',
    'PyQt5.QtHelp', 'PyQt5.QtNetwork', 'PyQt5.QtBluetooth',
    'PyQt5.QtPositioning', 'PyQt5.QtLocation', 'PyQt5.QtSensors',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉一些不必要的文件以减小体积
a.datas = [x for x in a.datas if not any(exclude in x[0].lower() for exclude in [
    'test', 'example', 'demo', 'doc', 'readme', 'license', 'changelog'
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智能票据处理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
{icon_line}
)
'''
    
    with open('智能票据处理系统_单包.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 已创建单包exe的spec文件: 智能票据处理系统_单包.spec")

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
        print("✅ PyInstaller已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("📦 PyInstaller未安装，正在安装...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller安装失败: {e}")
            return False

def build_single_exe():
    """执行单包exe打包"""
    print("🚀 开始单包exe打包...")
    
    # 使用spec文件打包
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        '智能票据处理系统_单包.spec'
    ]
    
    try:
        print("⏳ 正在打包，请耐心等待...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        print("✅ 单包exe打包成功！")
        
        # 检查生成的文件
        exe_path = os.path.join('dist', '智能票据处理系统.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            
            # 创建启动脚本
            create_launcher_script()
            
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if e.stdout:
            print(f"输出信息: {e.stdout}")
        return False

def create_launcher_script():
    """创建启动脚本"""
    launcher_content = '''@echo off
chcp 65001 >nul
echo ========================================
echo    智能票据处理系统 v2.0
echo ========================================
echo.
echo 正在启动程序...
echo.

cd /d "%~dp0"
start "" "智能票据处理系统.exe"

echo ✅ 程序已启动
echo 💡 如果程序无法启动，请检查系统是否安装了必要的运行库
echo.
pause
'''
    
    with open('dist/启动程序.bat', 'w', encoding='gbk') as f:
        f.write(launcher_content)
    print("✅ 已创建启动脚本: dist/启动程序.bat")

def create_readme():
    """创建使用说明"""
    readme_content = '''# 智能票据处理系统 - 单包版本

## 📦 文件说明
- 智能票据处理系统.exe - 主程序文件（单包版本）
- 启动程序.bat - 快速启动脚本

## 🚀 使用方法
1. 双击"智能票据处理系统.exe"直接运行
2. 或者双击"启动程序.bat"启动（推荐）

## 💡 注意事项
1. 首次启动可能需要较长时间，请耐心等待
2. 如果遇到杀毒软件误报，请添加信任
3. 确保系统已安装Visual C++ Redistributable
4. 建议在系统盘有足够空间的情况下运行

## 🔧 系统要求
- Windows 10/11 (64位)
- 至少4GB内存
- 至少500MB可用磁盘空间
- 稳定的网络连接

## 📞 技术支持
如遇问题，请检查：
1. 系统是否满足最低要求
2. 杀毒软件是否阻止运行
3. 网络连接是否正常
4. 是否有足够的磁盘空间

版本: v2.0 单包版
打包时间: ''' + str(__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 已创建使用说明: dist/使用说明.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 智能票据处理系统 - 单包EXE打包工具")
    print("=" * 60)
    
    # 检查并安装PyInstaller
    if not install_pyinstaller():
        return
    
    # 清理旧文件
    clean_build_dirs()
    
    # 创建spec文件
    create_single_exe_spec()
    
    # 执行打包
    if build_single_exe():
        # 创建使用说明
        create_readme()
        
        print("\n🎉 单包exe打包完成！")
        print("=" * 60)
        print("📁 输出目录: dist/")
        print("📦 主程序: dist/智能票据处理系统.exe")
        print("🚀 启动脚本: dist/启动程序.bat")
        print("📖 使用说明: dist/使用说明.txt")
        print("=" * 60)
        print("💡 提示:")
        print("1. 可以直接运行exe文件")
        print("2. 推荐使用启动脚本运行")
        print("3. 首次启动可能较慢，请耐心等待")
        print("4. 如遇杀毒软件误报，请添加信任")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()